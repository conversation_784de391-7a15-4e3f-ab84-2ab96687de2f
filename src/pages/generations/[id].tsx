import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { AppTemplate } from '@/components/templates';
import { NextPageWithLayout } from '../_app';
import { GenerationsLayout } from '@/components/templates/generations/GenerationsLayout';
import { ChatPanel } from '@/components/templates/generations/ChatPanel';
import { CodePreviewPanel } from '@/components/templates/generations/CodePreviewPanel';
import { Typography, Alert } from '@/components/atoms';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

interface GenerationData {
	id: string;
	prompt: string;
	type: 'DOCUMENTATION' | 'UI';
	description?: string;
	tags?: string[];
	projectId?: string;
	createdById: string;
	createdAt: string;
	updatedAt: string;
}

const GenerationPage: NextPageWithLayout = () => {
	const router = useRouter();
	const { id } = router.query;
	const [generation, setGeneration] = useState<GenerationData | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	// Mock data for now - will be replaced with real API call
	useEffect(() => {
		if (id) {
			// Simulate API call
			setTimeout(() => {
				setGeneration({
					id: id as string,
					prompt: "Create a modern dashboard with dark theme support, including charts, metrics cards, and a sidebar navigation",
					type: 'UI',
					description: 'Generated from home page',
					createdById: 'user-123',
					createdAt: new Date().toISOString(),
					updatedAt: new Date().toISOString(),
				});
				setIsLoading(false);
			}, 1000);
		}
	}, [id]);

	const handleBack = () => {
		router.back();
	};

	if (isLoading) {
		return (
			<div className='flex items-center justify-center min-h-screen bg-layout-50'>
				<div className='text-center'>
					<div className='animate-spin rounded-full h-12 w-12 border-2 border-border-secondary border-t-primary mx-auto mb-4'></div>
					<Typography variant='body' color='secondary'>
						Loading generation...
					</Typography>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className='p-6 max-w-2xl mx-auto'>
				<Alert variant='error' className='mb-4'>
					{error}
				</Alert>
			</div>
		);
	}

	if (!generation) {
		return (
			<div className='flex items-center justify-center min-h-screen bg-layout-50'>
				<div className='text-center'>
					<Typography variant='h3' color='secondary' className='mb-4'>
						Generation not found
					</Typography>
					<Typography variant='body' color='tertiary'>
						The generation you're looking for doesn't exist or has been removed.
					</Typography>
				</div>
			</div>
		);
	}

	return (
		<div className='h-screen flex flex-col bg-layout-50'>
			{/* Header */}
			<header className='flex-shrink-0 bg-surface-50 border-b border-border-secondary px-4 py-3'>
				<div className='flex items-center justify-between'>
					<div className='flex items-center gap-3'>
						<button
							onClick={handleBack}
							className='p-2 rounded-lg hover:bg-surface-100 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-200'
							aria-label='Go back'>
							<ArrowLeftIcon className='w-5 h-5 text-muted' />
						</button>
						<div>
							<Typography variant='h4' weight='semibold' className='mb-1'>
								{generation.type} Generation
							</Typography>
							<Typography variant='caption' color='tertiary'>
								Created {new Date(generation.createdAt).toLocaleDateString()}
							</Typography>
						</div>
					</div>
				</div>
			</header>

			{/* Main Content */}
			<div className='flex-1 flex overflow-hidden'>
				<GenerationsLayout
					chatPanel={<ChatPanel initialPrompt={generation.prompt} />}
					codePreviewPanel={<CodePreviewPanel />}
				/>
			</div>
		</div>
	);
};

GenerationPage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

export default GenerationPage;
