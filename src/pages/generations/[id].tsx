import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { AppTemplate } from '@/components/templates';
import { NextPageWithLayout } from '../_app';
import { GenerationsLayout } from '@/components/templates/generations/GenerationsLayout';
import { ChatPanel } from '@/components/templates/generations/ChatPanel';
import { CodePreviewPanel } from '@/components/templates/generations/CodePreviewPanel';
import { Typography, Alert, Badge } from '@/components/atoms';
import {
	ArrowLeftIcon,
	FolderIcon,
	UserIcon,
} from '@heroicons/react/24/outline';

// Types based on Prisma schema
interface GenerationData {
	id: string;
	type: 'UI' | 'DOCUMENTATION';
	prompt: string;
	status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
	result?: string;
	metadata?: any;
	createdAt: string;
	updatedAt: string;
	projectId: string;
	createdById: string;
	promptHistoryId?: string;
	// Relations
	project: {
		id: string;
		name: string;
		description?: string;
		status: 'ACTIVE' | 'INACTIVE' | 'ARCHIVED';
	};
	createdBy: {
		id: string;
		email: string;
		fullName?: string;
	};
	promptHistory?: {
		id: string;
		prompt: string;
		description?: string;
		tags: string[];
		usageCount: number;
	};
}

const GenerationPage: NextPageWithLayout = () => {
	const router = useRouter();
	const { id } = router.query;
	const [generation, setGeneration] = useState<GenerationData | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	// Mock data for now - will be replaced with real API call
	useEffect(() => {
		if (id) {
			// Simulate API call
			setTimeout(() => {
				setGeneration({
					id: id as string,
					type: 'UI',
					prompt:
						'Create a modern dashboard with dark theme support, including charts, metrics cards, and a sidebar navigation',
					status: 'COMPLETED',
					result: '// Generated code will be here',
					metadata: {
						files: ['App.tsx', 'Dashboard.tsx', 'Sidebar.tsx'],
						framework: 'React',
						styling: 'Tailwind CSS',
					},
					createdAt: new Date().toISOString(),
					updatedAt: new Date().toISOString(),
					projectId: 'project-123',
					createdById: 'user-123',
					promptHistoryId: 'prompt-123',
					project: {
						id: 'project-123',
						name: 'My Dashboard Project',
						description: 'A modern dashboard application',
						status: 'ACTIVE' as const,
					},
					createdBy: {
						id: 'user-123',
						email: '<EMAIL>',
						fullName: 'John Doe',
					},
					promptHistory: {
						id: 'prompt-123',
						prompt:
							'Create a modern dashboard with dark theme support, including charts, metrics cards, and a sidebar navigation',
						description: 'Dashboard generation prompt',
						tags: ['dashboard', 'dark-theme', 'charts'],
						usageCount: 1,
					},
				});
				setIsLoading(false);
			}, 1000);
		}
	}, [id]);

	const handleBack = () => {
		router.back();
	};

	if (isLoading) {
		return (
			<div className='flex items-center justify-center min-h-screen bg-layout-50'>
				<div className='text-center'>
					<div className='animate-spin rounded-full h-12 w-12 border-2 border-border-secondary border-t-primary mx-auto mb-4'></div>
					<Typography
						variant='body'
						color='secondary'>
						Loading generation...
					</Typography>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className='p-6 max-w-2xl mx-auto'>
				<Alert
					variant='error'
					className='mb-4'>
					{error}
				</Alert>
			</div>
		);
	}

	if (!generation) {
		return (
			<div className='flex items-center justify-center min-h-screen bg-layout-50'>
				<div className='text-center'>
					<Typography
						variant='h3'
						color='secondary'
						className='mb-4'>
						Generation not found
					</Typography>
					<Typography
						variant='body'
						color='tertiary'>
						The generation you're looking for doesn't exist or has been removed.
					</Typography>
				</div>
			</div>
		);
	}

	const getStatusBadge = (status: GenerationData['status']) => {
		switch (status) {
			case 'COMPLETED':
				return { variant: 'success' as const, text: 'Completed' };
			case 'IN_PROGRESS':
				return { variant: 'warning' as const, text: 'In Progress' };
			case 'PENDING':
				return { variant: 'secondary' as const, text: 'Pending' };
			case 'FAILED':
				return { variant: 'danger' as const, text: 'Failed' };
			default:
				return { variant: 'secondary' as const, text: 'Unknown' };
		}
	};

	const statusBadge = getStatusBadge(generation.status);

	return (
		<div className='h-screen flex flex-col bg-layout-50'>
			{/* Header */}
			<header className='flex-shrink-0 bg-surface-50 border-b border-border-secondary px-4 py-4'>
				<div className='flex items-center justify-between'>
					<div className='flex items-center gap-4'>
						<button
							onClick={handleBack}
							className='p-2 rounded-lg hover:bg-surface-100 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-200'
							aria-label='Go back'>
							<ArrowLeftIcon className='w-5 h-5 text-muted' />
						</button>

						<div className='flex items-center gap-4'>
							<div>
								<div className='flex items-center gap-2 mb-1'>
									<Typography
										variant='h4'
										weight='semibold'>
										{generation.type} Generation
									</Typography>
									<Badge
										variant={statusBadge.variant}
										size='small'
										emphasis='light'>
										{statusBadge.text}
									</Badge>
								</div>
								<div className='flex items-center gap-4 text-sm text-muted'>
									<div className='flex items-center gap-1'>
										<FolderIcon className='w-4 h-4' />
										<span>{generation.project.name}</span>
									</div>
									<div className='flex items-center gap-1'>
										<UserIcon className='w-4 h-4' />
										<span>
											{generation.createdBy.fullName ||
												generation.createdBy.email}
										</span>
									</div>
									<span>
										Created{' '}
										{new Date(generation.createdAt).toLocaleDateString()}
									</span>
								</div>
							</div>
						</div>
					</div>

					{/* Header Actions */}
					<div className='flex items-center gap-2'>
						{generation.promptHistory &&
							generation.promptHistory.tags.length > 0 && (
								<div className='flex items-center gap-1'>
									{generation.promptHistory.tags.slice(0, 3).map((tag) => (
										<Badge
											key={tag}
											variant='secondary'
											size='small'
											emphasis='light'
											className='text-xs'>
											{tag}
										</Badge>
									))}
								</div>
							)}
					</div>
				</div>
			</header>

			{/* Main Content */}
			<div className='flex-1 flex overflow-hidden'>
				<GenerationsLayout
					chatPanel={
						<ChatPanel
							generation={generation}
							onRegenerateCode={(prompt) => {
								// Handle code regeneration
								console.log('Regenerating with prompt:', prompt);
							}}
						/>
					}
					codePreviewPanel={
						<CodePreviewPanel
							generation={generation}
							onCodeUpdate={(code) => {
								// Handle code updates
								console.log('Code updated:', code);
							}}
						/>
					}
				/>
			</div>
		</div>
	);
};

GenerationPage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

export default GenerationPage;
