import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { AppTemplate } from '@/components/templates';
import { NextPageWithLayout } from '../_app';
import { <PERSON>po<PERSON>, <PERSON><PERSON>, Badge, Card, CardContent, Alert } from '@/components/atoms';
import { 
	PlusIcon, 
	SparklesIcon, 
	ClockIcon,
	FolderIcon,
	UserIcon,
	EyeIcon,
	CodeBracketIcon,
	DocumentTextIcon
} from '@heroicons/react/24/outline';

// Types based on Prisma schema
interface GenerationListItem {
	id: string;
	type: 'UI' | 'DOCUMENTATION';
	prompt: string;
	status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
	createdAt: string;
	updatedAt: string;
	project: {
		id: string;
		name: string;
	};
	createdBy: {
		id: string;
		email: string;
		fullName?: string;
	};
	promptHistory?: {
		tags: string[];
		usageCount: number;
	};
}

const GenerationsIndexPage: NextPageWithLayout = () => {
	const router = useRouter();
	const [generations, setGenerations] = useState<GenerationListItem[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	// Mock data for now - will be replaced with real API call
	useEffect(() => {
		// Simulate API call
		setTimeout(() => {
			setGenerations([
				{
					id: '1',
					type: 'UI',
					prompt: 'Create a modern dashboard with dark theme support',
					status: 'COMPLETED',
					createdAt: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
					updatedAt: new Date(Date.now() - 86400000).toISOString(),
					project: { id: 'proj-1', name: 'Dashboard Project' },
					createdBy: { id: 'user-1', email: '<EMAIL>', fullName: 'John Doe' },
					promptHistory: { tags: ['dashboard', 'dark-theme'], usageCount: 3 }
				},
				{
					id: '2',
					type: 'DOCUMENTATION',
					prompt: 'Generate API documentation for user authentication',
					status: 'IN_PROGRESS',
					createdAt: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
					updatedAt: new Date(Date.now() - 1800000).toISOString(), // 30 min ago
					project: { id: 'proj-2', name: 'Auth Service' },
					createdBy: { id: 'user-2', email: '<EMAIL>', fullName: 'Jane Smith' },
					promptHistory: { tags: ['api', 'auth', 'docs'], usageCount: 1 }
				},
				{
					id: '3',
					type: 'UI',
					prompt: 'Build a responsive landing page with hero section',
					status: 'FAILED',
					createdAt: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
					updatedAt: new Date(Date.now() - 7200000).toISOString(),
					project: { id: 'proj-3', name: 'Marketing Site' },
					createdBy: { id: 'user-1', email: '<EMAIL>', fullName: 'John Doe' },
					promptHistory: { tags: ['landing', 'responsive'], usageCount: 2 }
				}
			]);
			setIsLoading(false);
		}, 1000);
	}, []);

	const getStatusBadge = (status: GenerationListItem['status']) => {
		switch (status) {
			case 'COMPLETED':
				return { variant: 'success' as const, text: 'Completed' };
			case 'IN_PROGRESS':
				return { variant: 'warning' as const, text: 'In Progress' };
			case 'PENDING':
				return { variant: 'secondary' as const, text: 'Pending' };
			case 'FAILED':
				return { variant: 'danger' as const, text: 'Failed' };
			default:
				return { variant: 'secondary' as const, text: 'Unknown' };
		}
	};

	const handleViewGeneration = (id: string) => {
		router.push(`/generations/${id}`);
	};

	const handleCreateNew = () => {
		router.push('/');
	};

	if (isLoading) {
		return (
			<div className='flex items-center justify-center min-h-screen bg-layout-50'>
				<div className='text-center'>
					<div className='animate-spin rounded-full h-12 w-12 border-2 border-border-secondary border-t-primary mx-auto mb-4'></div>
					<Typography variant='body' color='secondary'>
						Loading generations...
					</Typography>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className='p-6 max-w-2xl mx-auto'>
				<Alert variant='error' className='mb-4'>
					{error}
				</Alert>
			</div>
		);
	}

	return (
		<div className='min-h-screen bg-layout-50'>
			<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'>
				{/* Header */}
				<div className='flex items-center justify-between mb-8'>
					<div>
						<Typography variant='h1' weight='bold' className='mb-2'>
							Generations
						</Typography>
						<Typography variant='body-lg' color='secondary'>
							View and manage your AI-generated content
						</Typography>
					</div>
					<Button
						variant='primary'
						size='md'
						onClick={handleCreateNew}
						className='flex items-center gap-2'>
						<PlusIcon className='w-5 h-5' />
						New Generation
					</Button>
				</div>

				{/* Generations Grid */}
				{generations.length > 0 ? (
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{generations.map((generation) => {
							const statusBadge = getStatusBadge(generation.status);
							return (
								<Card
									key={generation.id}
									variant='default'
									size='medium'
									className='hover:shadow-lg transition-all duration-200 cursor-pointer'
									onClick={() => handleViewGeneration(generation.id)}>
									<CardContent className='p-6'>
										{/* Header */}
										<div className='flex items-start justify-between mb-4'>
											<div className='flex items-center gap-2'>
												{generation.type === 'UI' ? (
													<CodeBracketIcon className='w-5 h-5 text-primary' />
												) : (
													<DocumentTextIcon className='w-5 h-5 text-accent-purple-600' />
												)}
												<Typography variant='body-sm' weight='medium' color='secondary'>
													{generation.type}
												</Typography>
											</div>
											<Badge
												variant={statusBadge.variant}
												size='small'
												emphasis='light'>
												{statusBadge.text}
											</Badge>
										</div>

										{/* Prompt */}
										<Typography 
											variant='body' 
											weight='medium' 
											className='mb-3 line-clamp-2 leading-relaxed'>
											{generation.prompt}
										</Typography>

										{/* Tags */}
										{generation.promptHistory && generation.promptHistory.tags.length > 0 && (
											<div className='flex flex-wrap gap-1 mb-4'>
												{generation.promptHistory.tags.slice(0, 3).map((tag) => (
													<Badge
														key={tag}
														variant='secondary'
														size='small'
														emphasis='light'
														className='text-xs'>
														{tag}
													</Badge>
												))}
												{generation.promptHistory.tags.length > 3 && (
													<Badge
														variant='secondary'
														size='small'
														emphasis='light'
														className='text-xs'>
														+{generation.promptHistory.tags.length - 3}
													</Badge>
												)}
											</div>
										)}

										{/* Footer */}
										<div className='flex items-center justify-between text-sm text-muted'>
											<div className='flex items-center gap-3'>
												<div className='flex items-center gap-1'>
													<FolderIcon className='w-4 h-4' />
													<span className='truncate max-w-24'>{generation.project.name}</span>
												</div>
												<div className='flex items-center gap-1'>
													<UserIcon className='w-4 h-4' />
													<span className='truncate max-w-20'>
														{generation.createdBy.fullName?.split(' ')[0] || generation.createdBy.email.split('@')[0]}
													</span>
												</div>
											</div>
											<div className='flex items-center gap-1'>
												<ClockIcon className='w-4 h-4' />
												<span>{new Date(generation.createdAt).toLocaleDateString()}</span>
											</div>
										</div>
									</CardContent>
								</Card>
							);
						})}
					</div>
				) : (
					<div className='text-center py-16'>
						<div className='p-4 bg-surface-50 rounded-2xl inline-block mb-6'>
							<SparklesIcon className='w-12 h-12 text-muted mx-auto' />
						</div>
						<Typography variant='h3' color='secondary' className='mb-4'>
							No generations yet
						</Typography>
						<Typography variant='body' color='tertiary' className='max-w-md mx-auto mb-6'>
							Start creating AI-generated content by describing what you want to build
						</Typography>
						<Button
							variant='primary'
							size='md'
							onClick={handleCreateNew}
							className='flex items-center gap-2 mx-auto'>
							<PlusIcon className='w-5 h-5' />
							Create Your First Generation
						</Button>
					</div>
				)}
			</div>
		</div>
	);
};

GenerationsIndexPage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

export default GenerationsIndexPage;
