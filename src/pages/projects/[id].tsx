import { useState, useCallback } from 'react';
import { useRouter } from 'next/router';
import {
	ArrowLeftIcon,
	PencilIcon,
	TrashIcon,
	UserGroupIcon,
} from '@heroicons/react/24/outline';
import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	Badge,
	Card,
	CardHeader,
	CardContent,
	DateDisplay,
} from '@/components/atoms';
import AppTemplate from '@/components/templates/AppTemplate';
import { MemberList } from '@/components/molecules';
import { useProjects } from '@/hooks/useProjects';
import {
	getStatusBadge,
	getProjectMemberUsers,
	getProjectMemberCount,
} from '@/components/templates/project/ProjectsGrid';
import type { NextPageWithLayout } from '../_app';
import type { Project } from '@/components/templates/project/ProjectsGrid';
import type { User } from '@/api/UserApi';
import { DeleteProjectDialog, ProjectFormDialog } from '@/components/templates';

const ProjectDetailPage: NextPageWithLayout = () => {
	const router = useRouter();
	const { id } = router.query;
	const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

	const {
		projects,
		updateProject,
		deleteProject,
		removeProjectMembers,
		isLoading,
		canManageProjects,
	} = useProjects();

	const project = projects.find((p) => p.id === id) as Project | undefined;
	const handleBack = useCallback(() => {
		router.push('/projects');
	}, [router]);

	const handleEdit = useCallback(() => {
		setIsEditDialogOpen(true);
	}, []);

	const handleDelete = useCallback(() => {
		setIsDeleteDialogOpen(true);
	}, []);

	const handleProjectUpdated = useCallback(
		async (
			projectId: string,
			projectData: {
				name: string;
				description: string;
				members?: User[];
			},
		) => {
			const updatedProject = await updateProject(projectId, projectData);
			if (updatedProject) {
				setIsEditDialogOpen(false);
			}
			return updatedProject;
		},
		[updateProject],
	);

	const handleRemoveMember = useCallback(
		async (member: User) => {
			if (!project || !canManageProjects) return;

			// Use the dedicated remove members API
			const updatedProject = await removeProjectMembers(project.id, [
				member.id,
			]);

			if (!updatedProject) {
				// Handle error - could show a toast notification
				console.error('Failed to remove member');
			}
		},
		[project, canManageProjects, removeProjectMembers],
	);

	const handleProjectDeleted = useCallback(
		async (projectId: string) => {
			const success = await deleteProject(projectId);
			if (success) {
				setIsDeleteDialogOpen(false);
				router.push('/projects');
			}
			return success;
		},
		[deleteProject, router],
	);

	const handleCloseEditDialog = useCallback(() => {
		setIsEditDialogOpen(false);
	}, []);

	const handleCloseDeleteDialog = useCallback(() => {
		setIsDeleteDialogOpen(false);
	}, []);
	if (isLoading) {
		return (
			<div className='p-4 sm:p-6'>
				<div className='animate-pulse space-y-6'>
					<div className='h-8 bg-secondary rounded w-1/4'></div>
					<div className='h-12 bg-secondary rounded w-1/2'></div>
					<div className='h-32 bg-secondary rounded'></div>
				</div>
			</div>
		);
	}
	if (!project) {
		return (
			<div className='p-2 sm:p-3'>
				<div className='flex flex-col items-center justify-center py-12'>
					<Typography
						variant='h3'
						color='secondary'
						className='mb-2 text-center'>
						Project not found
					</Typography>
					<Typography
						variant='body'
						color='tertiary'
						className='text-center mb-4'>
						The project you&rsquo;re looking for doesn&rsquo;t exist or has been
						removed.
					</Typography>
					<Button
						variant='primary'
						onClick={handleBack}>
						Back to Projects
					</Button>
				</div>
			</div>
		);
	}

	const statusBadge = getStatusBadge(project.status);

	return (
		<div className='p-2 sm:p-3 max-w-4xl mx-auto'>
			<div className='mb-4'>
				<div className='mb-3'>
					<Button
						variant='link'
						size='sm'
						onClick={handleBack}
						className='mb-2'>
						<ArrowLeftIcon className='w-4 h-4 mr-2' />
						Back to Projects
					</Button>
				</div>
				<div className='flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2 sm:gap-3'>
					<div className='flex-1 min-w-0'>
						<div className='flex items-start gap-2 mb-2'>
							<Typography
								variant='h1'
								responsive
								className='flex-1 min-w-0'>
								{project.name}
							</Typography>
						</div>
						<Typography
							variant='body-lg'
							color='secondary'
							className='mb-2'>
							{project.description}
						</Typography>
					</div>

					{canManageProjects && (
						<div className='flex gap-1.5 flex-shrink-0'>
							<Button
								variant='secondary'
								size='md'
								onClick={handleEdit}>
								<PencilIcon className='w-4 h-4 mr-2' />
								<span className='hidden sm:inline'>Edit Project</span>
								<span className='sm:hidden'>Edit</span>
							</Button>
							<Button
								variant='danger'
								size='md'
								onClick={handleDelete}>
								<TrashIcon className='w-4 h-4 mr-2' />
								<span className='hidden sm:inline'>Delete Project</span>
								<span className='sm:hidden'>Delete</span>
							</Button>
						</div>
					)}
				</div>
			</div>

			<div className='grid grid-cols-1 lg:grid-cols-3 gap-3 mb-4'>
				<div className='lg:col-span-2 space-y-3'>
					<Card
						variant='default'
						size='large'>
						<CardHeader>
							<Typography
								variant='h4'
								weight='semibold'>
								Project Overview
							</Typography>
						</CardHeader>
						<CardContent>
							<div className='space-y-2'>
								<div>
									<Typography
										variant='body-sm'
										weight='medium'
										color='secondary'
										className='mb-1'>
										Description
									</Typography>
									<Typography variant='body'>{project.description}</Typography>
								</div>

								<div>
									<Typography
										variant='body-sm'
										weight='medium'
										color='secondary'
										className='mb-1'>
										Status
									</Typography>
									<Badge
										variant={statusBadge.variant}
										size='small'
										emphasis='light'>
										{statusBadge.text}
									</Badge>
								</div>
							</div>
						</CardContent>
					</Card>
				</div>

				<div className='space-y-3'>
					<Card
						variant='default'
						size='medium'>
						<CardHeader>
							<Typography
								variant='h5'
								weight='semibold'>
								Project Details
							</Typography>
						</CardHeader>
						<CardContent>
							<div className='space-y-2'>
								<div>
									<Typography
										variant='body-sm'
										weight='medium'
										color='secondary'
										className='mb-0.5'>
										Created
									</Typography>
									<DateDisplay
										date={project.createdAt}
										format='full'
										variant='body-sm'
										showTooltip
									/>
								</div>

								<div>
									<Typography
										variant='body-sm'
										weight='medium'
										color='secondary'
										className='mb-0.5'>
										Last Updated
									</Typography>
									<DateDisplay
										date={project.updatedAt}
										format='smart'
										variant='body-sm'
										showTooltip
									/>
								</div>

								{project._count && (
									<div>
										<Typography
											variant='body-sm'
											weight='medium'
											color='secondary'
											className='mb-0.5'>
											Statistics
										</Typography>
										<div className='flex items-center gap-4 text-sm text-muted'>
											{project._count.generations !== undefined && (
												<span>
													{project._count.generations} generation
													{project._count.generations !== 1 ? 's' : ''}
												</span>
											)}
											{project._count.promptHistory !== undefined && (
												<span>
													{project._count.promptHistory} prompt
													{project._count.promptHistory !== 1 ? 's' : ''}
												</span>
											)}
										</div>
									</div>
								)}
							</div>
						</CardContent>
					</Card>

					{/* Team Members Card */}
					<Card
						variant='default'
						size='medium'>
						<CardHeader>
							<div className='flex items-center justify-between'>
								<Typography
									variant='h5'
									weight='semibold'
									className='flex items-center gap-2'>
									<UserGroupIcon className='w-5 h-5' />
									Team Members
								</Typography>
								{getProjectMemberCount(project.members) > 0 && (
									<Typography
										variant='caption'
										color='tertiary'>
										{getProjectMemberCount(project.members)} member
										{getProjectMemberCount(project.members) !== 1 ? 's' : ''}
									</Typography>
								)}
							</div>
						</CardHeader>
						<CardContent>
							<MemberList
								members={getProjectMemberUsers(project.members)}
								onRemoveMember={handleRemoveMember}
								canManageMembers={canManageProjects}
								showRemoveButton={true}
							/>
						</CardContent>
					</Card>
				</div>
			</div>

			{isEditDialogOpen && (
				<ProjectFormDialog
					isOpen={isEditDialogOpen}
					onClose={handleCloseEditDialog}
					mode='edit'
					project={project}
					onProjectUpdated={handleProjectUpdated}
				/>
			)}

			{isDeleteDialogOpen && (
				<DeleteProjectDialog
					isOpen={isDeleteDialogOpen}
					onClose={handleCloseDeleteDialog}
					project={project}
					onProjectDeleted={handleProjectDeleted}
				/>
			)}
		</div>
	);
};

ProjectDetailPage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

export default ProjectDetailPage;
