import { useState, useCallback } from 'react';
import { useRouter } from 'next/router';
import {
	ArrowLeftIcon,
	PencilIcon,
	TrashIcon,
	UserGroupIcon,
} from '@heroicons/react/24/outline';
import {
	<PERSON>po<PERSON>,
	<PERSON>ton,
	Badge,
	Card,
	CardHeader,
	CardContent,
	DateDisplay,
} from '@/components/atoms';
import AppTemplate from '@/components/templates/AppTemplate';

import { useProjects } from '@/hooks/useProjects';
import {
	getStatusBadge,
	getProjectMemberUsers,
	getProjectMemberCount,
} from '@/components/templates/project/ProjectsGrid';
import type { NextPageWithLayout } from '../_app';
import type { Project } from '@/components/templates/project/ProjectsGrid';
import type { User } from '@/api/UserApi';
import {
	DeleteProjectDialog,
	MemberList,
	ProjectFormDialog,
} from '@/components/templates';

const ProjectDetailPage: NextPageWithLayout = () => {
	const router = useRouter();
	const { id } = router.query;
	const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

	const {
		projects,
		updateProject,
		deleteProject,
		removeProjectMembers,
		isLoading,
		canManageProjects,
	} = useProjects();

	const project = projects.find((p) => p.id === id) as Project | undefined;
	const handleBack = useCallback(() => {
		router.push('/projects');
	}, [router]);

	const handleEdit = useCallback(() => {
		setIsEditDialogOpen(true);
	}, []);

	const handleDelete = useCallback(() => {
		setIsDeleteDialogOpen(true);
	}, []);

	const handleProjectUpdated = useCallback(
		async (
			projectId: string,
			projectData: {
				name: string;
				description: string;
				members?: User[];
			},
		) => {
			const updatedProject = await updateProject(projectId, projectData);
			if (updatedProject) {
				setIsEditDialogOpen(false);
			}
			return updatedProject;
		},
		[updateProject],
	);

	const handleRemoveMember = useCallback(
		async (member: User) => {
			if (!project || !canManageProjects) return;

			const updatedProject = await removeProjectMembers(project.id, [
				member.id,
			]);

			if (!updatedProject) {
				console.error('Failed to remove member');
			}
		},
		[project, canManageProjects, removeProjectMembers],
	);

	const handleProjectDeleted = useCallback(
		async (projectId: string) => {
			const success = await deleteProject(projectId);
			if (success) {
				setIsDeleteDialogOpen(false);
				router.push('/projects');
			}
			return success;
		},
		[deleteProject, router],
	);

	const handleCloseEditDialog = useCallback(() => {
		setIsEditDialogOpen(false);
	}, []);

	const handleCloseDeleteDialog = useCallback(() => {
		setIsDeleteDialogOpen(false);
	}, []);
	if (isLoading) {
		return (
			<div className='p-6 sm:p-8 max-w-4xl mx-auto bg-layout-100 min-h-screen'>
				<div className='animate-pulse space-y-8'>
					<div className='h-8 bg-component-100 rounded-lg w-1/4'></div>
					<div className='h-12 bg-component-100 rounded-lg w-1/2'></div>
					<div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
						<div className='lg:col-span-2 h-64 bg-component-100 rounded-xl'></div>
						<div className='space-y-4'>
							<div className='h-32 bg-component-100 rounded-xl'></div>
							<div className='h-48 bg-component-100 rounded-xl'></div>
						</div>
					</div>
				</div>
			</div>
		);
	}
	if (!project) {
		return (
			<div className='p-6 sm:p-8 max-w-4xl mx-auto bg-layout-100 min-h-screen'>
				<div className='flex flex-col items-center justify-center py-20'>
					<div className='bg-surface-50 rounded-2xl p-8 shadow-sm text-center max-w-md'>
						<div className='w-16 h-16 bg-component-100 rounded-full flex items-center justify-center mx-auto mb-4'>
							<svg
								className='w-8 h-8 text-muted'
								fill='none'
								stroke='currentColor'
								viewBox='0 0 24 24'>
								<path
									strokeLinecap='round'
									strokeLinejoin='round'
									strokeWidth={1.5}
									d='M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
								/>
							</svg>
						</div>
						<Typography
							variant='h3'
							color='secondary'
							className='mb-3'>
							Project not found
						</Typography>
						<Typography
							variant='body'
							color='tertiary'
							className='mb-6 leading-relaxed'>
							The project you&rsquo;re looking for doesn&rsquo;t exist or has
							been removed.
						</Typography>
						<Button
							variant='primary'
							onClick={handleBack}
							className='shadow-sm'>
							<ArrowLeftIcon className='w-4 h-4 mr-2' />
							Back to Projects
						</Button>
					</div>
				</div>
			</div>
		);
	}

	const statusBadge = getStatusBadge(project.status);

	return (
		<div className='p-6 sm:p-8 max-w-6xl mx-auto bg-layout-100 min-h-screen'>
			{/* Header */}
			<div className='bg-surface-50 rounded-xl shadow-sm p-6 mb-8'>
				<div className='mb-4'>
					<Button
						variant='ghost'
						size='sm'
						onClick={handleBack}
						className='hover:bg-component-50'>
						<ArrowLeftIcon className='w-4 h-4 mr-2' />
						Back to Projects
					</Button>
				</div>
				<div className='flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4 sm:gap-6'>
					<div className='flex-1 min-w-0'>
						<div className='flex items-start gap-3 mb-3'>
							<Typography
								variant='h1'
								responsive
								className='flex-1 min-w-0'>
								{project.name}
							</Typography>
						</div>
						<Typography
							variant='body-lg'
							color='secondary'
							className='mb-3 leading-relaxed'>
							{project.description}
						</Typography>
						<Badge
							variant={statusBadge.variant}
							size='medium'
							emphasis='light'
							className='inline-flex'>
							{statusBadge.text}
						</Badge>
					</div>

					{canManageProjects && (
						<div className='flex gap-2 flex-shrink-0 p-1 bg-component-50 rounded-lg'>
							<Button
								variant='secondary'
								size='md'
								onClick={handleEdit}
								className='shadow-sm'>
								<PencilIcon className='w-4 h-4 mr-2' />
								<span className='hidden sm:inline'>Edit Project</span>
								<span className='sm:hidden'>Edit</span>
							</Button>
							<Button
								variant='danger'
								size='md'
								onClick={handleDelete}
								className='shadow-sm'>
								<TrashIcon className='w-4 h-4 mr-2' />
								<span className='hidden sm:inline'>Delete Project</span>
								<span className='sm:hidden'>Delete</span>
							</Button>
						</div>
					)}
				</div>
			</div>

			<div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
				<div className='lg:col-span-2 space-y-6'>
					<Card
						variant='default'
						size='large'
						className='bg-surface-50 shadow-sm'>
						<CardHeader className='pb-4'>
							<Typography
								variant='h4'
								weight='semibold'
								className='flex items-center gap-2'>
								<svg
									className='w-5 h-5 text-accent-teal-600'
									fill='none'
									stroke='currentColor'
									viewBox='0 0 24 24'>
									<path
										strokeLinecap='round'
										strokeLinejoin='round'
										strokeWidth={1.5}
										d='M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
									/>
								</svg>
								Project Overview
							</Typography>
						</CardHeader>
						<CardContent>
							<div className='space-y-4'>
								<div className='bg-component-50 p-4 rounded-lg'>
									<Typography
										variant='body-sm'
										weight='semibold'
										color='secondary'
										className='mb-2 uppercase tracking-wide'>
										Description
									</Typography>
									<Typography
										variant='body'
										className='leading-relaxed'>
										{project.description}
									</Typography>
								</div>

								<div className='bg-layout-100 p-4 rounded-lg'>
									<Typography
										variant='body-sm'
										weight='semibold'
										color='secondary'
										className='mb-2 uppercase tracking-wide'>
										Current Status
									</Typography>
									<Badge
										variant={statusBadge.variant}
										size='medium'
										emphasis='light'>
										{statusBadge.text}
									</Badge>
								</div>
							</div>
						</CardContent>
					</Card>
				</div>

				<div className='space-y-6'>
					<Card
						variant='default'
						size='medium'
						className='bg-surface-50 shadow-sm'>
						<CardHeader className='pb-4'>
							<Typography
								variant='h5'
								weight='semibold'
								className='flex items-center gap-2'>
								<svg
									className='w-5 h-5 text-accent-purple-600'
									fill='none'
									stroke='currentColor'
									viewBox='0 0 24 24'>
									<path
										strokeLinecap='round'
										strokeLinejoin='round'
										strokeWidth={1.5}
										d='M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2'
									/>
								</svg>
								Project Details
							</Typography>
						</CardHeader>
						<CardContent>
							<div className='space-y-4'>
								<div className='bg-component-50 p-3 rounded-lg'>
									<Typography
										variant='caption'
										weight='semibold'
										color='tertiary'
										className='mb-1 uppercase tracking-wide'>
										Created
									</Typography>{' '}
									<DateDisplay
										date={project.createdAt}
										format='full'
										variant='body-sm'
										showTooltip
									/>
								</div>

								<div className='bg-component-50 p-3 rounded-lg'>
									<Typography
										variant='caption'
										weight='semibold'
										color='tertiary'
										className='mb-1 uppercase tracking-wide'>
										Last Updated
									</Typography>{' '}
									<DateDisplay
										date={project.updatedAt}
										format='smart'
										variant='body-sm'
										showTooltip
									/>
								</div>

								{project._count && (
									<div className='bg-layout-100 p-3 rounded-lg'>
										<Typography
											variant='caption'
											weight='semibold'
											color='tertiary'
											className='mb-2 uppercase tracking-wide'>
											Statistics
										</Typography>
										<div className='space-y-1'>
											{project._count.generations !== undefined && (
												<div className='flex items-center gap-2'>
													<div className='w-2 h-2 bg-accent-teal-500 rounded-full'></div>
													<Typography
														variant='body-sm'
														color='secondary'>
														{project._count.generations} generation
														{project._count.generations !== 1 ? 's' : ''}
													</Typography>
												</div>
											)}
											{project._count.promptHistory !== undefined && (
												<div className='flex items-center gap-2'>
													<div className='w-2 h-2 bg-accent-orange-500 rounded-full'></div>
													<Typography
														variant='body-sm'
														color='secondary'>
														{project._count.promptHistory} prompt
														{project._count.promptHistory !== 1 ? 's' : ''}
													</Typography>
												</div>
											)}
										</div>
									</div>
								)}
							</div>
						</CardContent>
					</Card>

					{/* Team Members Card */}
					<Card
						variant='default'
						size='medium'
						className='bg-surface-50 shadow-sm'>
						<CardHeader className='pb-4'>
							<div className='flex items-center justify-between'>
								<Typography
									variant='h5'
									weight='semibold'
									className='flex items-center gap-2'>
									<UserGroupIcon className='w-5 h-5 text-accent-orange-600' />
									Team Members
								</Typography>
								{getProjectMemberCount(project.members) > 0 && (
									<div className='bg-component-100 px-2 py-1 rounded-md'>
										<Typography
											variant='caption'
											color='secondary'
											weight='medium'>
											{getProjectMemberCount(project.members)} member
											{getProjectMemberCount(project.members) !== 1 ? 's' : ''}
										</Typography>
									</div>
								)}
							</div>
						</CardHeader>
						<CardContent>
							<div className='bg-component-50 p-3 rounded-lg'>
								<MemberList
									members={getProjectMemberUsers(project.members)}
									onRemoveMember={handleRemoveMember}
									canManageMembers={canManageProjects}
									showRemoveButton={true}
								/>
							</div>
						</CardContent>
					</Card>
				</div>
			</div>

			{isEditDialogOpen && (
				<ProjectFormDialog
					isOpen={isEditDialogOpen}
					onClose={handleCloseEditDialog}
					mode='edit'
					project={project}
					onProjectUpdated={handleProjectUpdated}
				/>
			)}

			{isDeleteDialogOpen && (
				<DeleteProjectDialog
					isOpen={isDeleteDialogOpen}
					onClose={handleCloseDeleteDialog}
					project={project}
					onProjectDeleted={handleProjectDeleted}
				/>
			)}
		</div>
	);
};

ProjectDetailPage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

export default ProjectDetailPage;
