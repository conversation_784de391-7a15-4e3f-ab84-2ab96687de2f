import { useState } from 'react';
import { AppTemplate } from '@/components/templates';
import { Typo<PERSON>, Button, Alert } from '@/components/atoms';
import { NextPageWithLayout } from './_app';
import { PromptInput } from '@/components/molecules';
import { usePrompt } from '@/hooks/usePrompt';
import {
	SparklesIcon,
	PlusIcon,
	ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';

const HomePage: NextPageWithLayout = () => {
	const [prompt, setPrompt] = useState('');
	const { isLoading, error, savePrompt, clearError } = usePrompt();

	const handleGenerate = async (promptText: string) => {
		const result = await savePrompt({
			prompt: promptText,
			type: 'UI',
			description: 'Generated from home page',
		});

		if (result) {
			setPrompt('');
		}
	};

	return (
		<div className='min-h-screen gradient-bg'>
			{/* Hero Section */}
			<section className='relative'>
				<div className='max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pt-12 pb-16'>
					{/* Header */}
					<div className='text-center mb-12'>
						<div className='flex items-center justify-center mb-6'>
							<div className='p-3 bg-primary-100 rounded-2xl'>
								<SparklesIcon className='w-8 h-8 text-primary' />
							</div>
						</div>
						<Typography
							variant='display-sm'
							align='center'
							className='mb-4 gradient-text'>
							Generate Beautiful UIs with AI
						</Typography>
						<Typography
							variant='body-lg'
							color='secondary'
							align='center'
							className='max-w-2xl mx-auto leading-relaxed'>
							Describe your vision and watch it come to life. Create stunning
							user interfaces, components, and layouts with the power of
							artificial intelligence.
						</Typography>
					</div>

					{/* Main Prompt Input */}
					<div className='max-w-3xl mx-auto'>
						{/* Error Alert */}
						{error && (
							<Alert
								variant='negative'
								className='mb-4'>
								<ExclamationTriangleIcon className='w-4 h-4' />
								<span>{error}</span>
								<button
									onClick={clearError}
									className='ml-auto text-danger hover:text-danger-hover'>
									×
								</button>
							</Alert>
						)}

						<PromptInput
							value={prompt}
							onChange={setPrompt}
							onSubmit={handleGenerate}
							placeholder="Describe the UI you want to create... (e.g., 'Create a modern dashboard with dark theme')"
							loading={isLoading}
							submitButtonText='Generate'
							size='medium'
							maxLength={2000}
							autoFocus
							textareaClassName='bg-transparent'
							buttonClassName='shadow-lg'
						/>

						{/* Quick Actions */}
						<div className='flex flex-wrap items-center justify-center gap-2 mt-6'>
							<Typography
								variant='caption'
								color='tertiary'
								className='mr-2'>
								Try:
							</Typography>
							{[
								'Modern dashboard',
								'Landing page',
								'E-commerce card',
								'Login form',
							].map((suggestion) => (
								<Button
									key={suggestion}
									variant='ghost'
									size='sm'
									onClick={() => setPrompt(suggestion)}
									className='text-xs bg-surface hover:bg-secondary'>
									{suggestion}
								</Button>
							))}
						</div>
					</div>
				</div>
			</section>

			{/* Call to Action */}
			<section className='max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pb-16'>
				<div className='text-center py-16'>
					<div className='p-4 bg-surface-50 rounded-2xl inline-block mb-6'>
						<SparklesIcon className='w-12 h-12 text-primary mx-auto' />
					</div>
					<Typography
						variant='h3'
						align='center'
						className='mb-4'>
						Ready to Create Something Amazing?
					</Typography>
					<Typography
						variant='body-lg'
						color='secondary'
						align='center'
						className='max-w-2xl mx-auto mb-8 leading-relaxed'>
						Start by describing the UI you want to create in the prompt above.
						Our AI will help you generate beautiful, functional interfaces.
					</Typography>
					<Button
						variant='outline'
						size='md'
						className='inline-flex items-center gap-2'>
						<PlusIcon className='w-4 h-4' />
						Explore Projects
					</Button>
				</div>
			</section>
		</div>
	);
};

HomePage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

export default HomePage;
