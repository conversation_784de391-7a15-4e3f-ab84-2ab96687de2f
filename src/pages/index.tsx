import { useState } from 'react';
import { AppTemplate } from '@/components/templates';
import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>,
	CardContent,
	<PERSON><PERSON>,
	<PERSON><PERSON>,
} from '@/components/atoms';
import { NextPageWithLayout } from './_app';
import { PromptInput } from '@/components/molecules';
import { useGenerations } from '@/hooks/useGenerations';
import {
	SparklesIcon,
	ClockIcon,
	EyeIcon,
	ArrowRightIcon,
	PlusIcon,
	ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';

const HomePage: NextPageWithLayout = () => {
	const [prompt, setPrompt] = useState('');
	const { isGenerating, error, generateUI, clearError, getRecentGenerations } =
		useGenerations();

	const recentGenerations = getRecentGenerations(6);

	const handleGenerate = async (promptText: string) => {
		await generateUI(promptText);
		setPrompt(''); // Clear the input after successful generation
	};

	return (
		<div className='min-h-screen gradient-bg'>
			{/* Hero Section */}
			<section className='relative'>
				<div className='max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pt-12 pb-16'>
					{/* Header */}
					<div className='text-center mb-12'>
						<div className='flex items-center justify-center mb-6'>
							<div className='p-3 bg-primary-100 rounded-2xl'>
								<SparklesIcon className='w-8 h-8 text-primary' />
							</div>
						</div>
						<Typography
							variant='display-sm'
							className='mb-4 gradient-text'>
							Generate Beautiful UIs with AI
						</Typography>
						<Typography
							variant='body-lg'
							color='secondary'
							className='max-w-2xl mx-auto leading-relaxed'>
							Describe your vision and watch it come to life. Create stunning
							user interfaces, components, and layouts with the power of
							artificial intelligence.
						</Typography>
					</div>

					{/* Main Prompt Input */}
					<div className='max-w-3xl mx-auto'>
						{/* Error Alert */}
						{error && (
							<Alert
								variant='negative'
								className='mb-4'>
								<ExclamationTriangleIcon className='w-4 h-4' />
								<span>{error}</span>
								<button
									onClick={clearError}
									className='ml-auto text-danger hover:text-danger-hover'>
									×
								</button>
							</Alert>
						)}

						<PromptInput
							value={prompt}
							onChange={setPrompt}
							onSubmit={handleGenerate}
							placeholder="Describe the UI you want to create... (e.g., 'Create a modern dashboard with dark theme')"
							loading={isGenerating}
							submitButtonText='Generate'
							size='large'
							maxLength={2000}
							autoFocus
							className='shadow-xl glass-surface'
							textareaClassName='bg-transparent'
							buttonClassName='shadow-lg'
						/>

						{/* Quick Actions */}
						<div className='flex flex-wrap items-center justify-center gap-2 mt-6'>
							<Typography
								variant='caption'
								color='tertiary'
								className='mr-2'>
								Try:
							</Typography>
							{[
								'Modern dashboard',
								'Landing page',
								'E-commerce card',
								'Login form',
							].map((suggestion) => (
								<Button
									key={suggestion}
									variant='ghost'
									size='sm'
									onClick={() => setPrompt(suggestion)}
									className='text-xs bg-surface hover:bg-secondary'>
									{suggestion}
								</Button>
							))}
						</div>
					</div>
				</div>
			</section>

			{/* Recent Generations */}
			<section className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16'>
				<div className='flex items-center justify-between mb-8'>
					<div>
						<Typography
							variant='h2'
							className='mb-2'>
							Recent Generations
						</Typography>
						<Typography
							variant='body'
							color='secondary'>
							Your latest AI-generated UIs and components
						</Typography>
					</div>
					<Button
						variant='outline'
						size='sm'
						className='hidden sm:flex items-center gap-2'>
						<PlusIcon className='w-4 h-4' />
						New Project
					</Button>
				</div>

				{recentGenerations.length > 0 ? (
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
						{recentGenerations.map((generation) => (
							<Card
								key={generation.id}
								className='group hover:shadow-lg transition-all duration-200 glass-surface'>
								<CardContent className='p-0'>
									{/* Preview Image */}
									<div className='aspect-video gradient-primary-subtle rounded-t-lg relative overflow-hidden'>
										<div className='absolute inset-0 gradient-primary-overlay' />
										<div className='absolute inset-0 flex items-center justify-center'>
											<div className='text-center'>
												<SparklesIcon className='w-8 h-8 text-primary-600 mx-auto mb-2' />
												<Typography
													variant='caption'
													color='tertiary'>
													Generated UI Preview
												</Typography>
											</div>
										</div>

										{/* Status Badge */}
										<div className='absolute top-3 right-3'>
											<Badge
												variant='success'
												size='small'
												className='bg-success/10 text-success border-success/20'>
												Completed
											</Badge>
										</div>
									</div>

									{/* Content */}
									<div className='p-4'>
										<Typography
											variant='body-sm'
											className='line-clamp-2 mb-3 leading-relaxed'>
											{generation.prompt}
										</Typography>

										<div className='flex items-center justify-between text-xs'>
											<div className='flex items-center gap-3 text-muted'>
												<div className='flex items-center gap-1'>
													<ClockIcon className='w-3 h-3' />
													<span>{generation.timestamp}</span>
												</div>
												<div className='flex items-center gap-1'>
													<EyeIcon className='w-3 h-3' />
													<span>{generation.views}</span>
												</div>
											</div>

											<Button
												variant='ghost'
												size='sm'
												className='opacity-0 group-hover:opacity-100 transition-opacity text-xs h-6 px-2'>
												View
												<ArrowRightIcon className='w-3 h-3 ml-1' />
											</Button>
										</div>
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				) : (
					<div className='text-center py-16'>
						<div className='p-4 bg-surface rounded-2xl inline-block mb-4'>
							<SparklesIcon className='w-12 h-12 text-muted mx-auto' />
						</div>
						<Typography
							variant='h4'
							color='secondary'
							className='mb-2'>
							No generations yet
						</Typography>
						<Typography
							variant='body'
							color='tertiary'
							className='max-w-md mx-auto'>
							Start by describing the UI you want to create in the prompt above
						</Typography>
					</div>
				)}
			</section>
		</div>
	);
};

HomePage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

export default HomePage;
