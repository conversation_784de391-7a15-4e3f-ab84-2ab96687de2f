import { useState } from 'react';
import { Button } from '@/components/atoms';
import { 
	ChevronLeftIcon, 
	ChevronRightIcon,
	ChatBubbleLeftRightIcon,
	CodeBracketIcon,
	EyeIcon
} from '@heroicons/react/24/outline';

interface GenerationsLayoutProps {
	chatPanel: React.ReactNode;
	codePreviewPanel: React.ReactNode;
}

export const GenerationsLayout: React.FC<GenerationsLayoutProps> = ({
	chatPanel,
	codePreviewPanel,
}) => {
	const [isChatPanelCollapsed, setIsChatPanelCollapsed] = useState(false);
	const [activeRightPanel, setActiveRightPanel] = useState<'code' | 'preview'>('preview');

	const toggleChatPanel = () => {
		setIsChatPanelCollapsed(!isChatPanelCollapsed);
	};

	return (
		<div className='flex h-full'>
			{/* Chat Panel */}
			<div 
				className={`
					flex-shrink-0 bg-surface-50 border-r border-border-secondary transition-all duration-300 ease-in-out
					${isChatPanelCollapsed ? 'w-0' : 'w-80 lg:w-96'}
				`}
			>
				<div className={`h-full ${isChatPanelCollapsed ? 'hidden' : 'block'}`}>
					{/* Chat Panel Header */}
					<div className='flex items-center justify-between p-4 border-b border-border-secondary bg-surface-100'>
						<div className='flex items-center gap-2'>
							<ChatBubbleLeftRightIcon className='w-5 h-5 text-primary' />
							<span className='font-medium text-foreground'>Chat</span>
						</div>
						<Button
							variant='ghost'
							size='sm'
							onClick={toggleChatPanel}
							className='p-1.5'
							aria-label='Collapse chat panel'>
							<ChevronLeftIcon className='w-4 h-4' />
						</Button>
					</div>
					
					{/* Chat Panel Content */}
					<div className='h-full pb-16'>
						{chatPanel}
					</div>
				</div>
			</div>

			{/* Collapsed Chat Panel Toggle */}
			{isChatPanelCollapsed && (
				<div className='flex-shrink-0 w-12 bg-surface-50 border-r border-border-secondary flex flex-col items-center py-4'>
					<Button
						variant='ghost'
						size='sm'
						onClick={toggleChatPanel}
						className='p-2 mb-2'
						aria-label='Expand chat panel'>
						<ChevronRightIcon className='w-4 h-4' />
					</Button>
					<div className='writing-mode-vertical text-xs text-muted font-medium'>
						Chat
					</div>
				</div>
			)}

			{/* Code/Preview Panel */}
			<div className='flex-1 flex flex-col bg-layout-100'>
				{/* Code/Preview Panel Header */}
				<div className='flex-shrink-0 bg-surface-50 border-b border-border-secondary'>
					<div className='flex items-center justify-between p-4'>
						<div className='flex items-center gap-1'>
							<Button
								variant={activeRightPanel === 'code' ? 'primary' : 'ghost'}
								size='sm'
								onClick={() => setActiveRightPanel('code')}
								className='flex items-center gap-2 px-3 py-2'>
								<CodeBracketIcon className='w-4 h-4' />
								Code
							</Button>
							<Button
								variant={activeRightPanel === 'preview' ? 'primary' : 'ghost'}
								size='sm'
								onClick={() => setActiveRightPanel('preview')}
								className='flex items-center gap-2 px-3 py-2'>
								<EyeIcon className='w-4 h-4' />
								Preview
							</Button>
						</div>
						
						<div className='flex items-center gap-2'>
							<Button
								variant='outline'
								size='sm'
								className='flex items-center gap-2'>
								<svg className='w-4 h-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
									<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4' />
								</svg>
								Export
							</Button>
							<Button
								variant='outline'
								size='sm'
								className='flex items-center gap-2'>
								<svg className='w-4 h-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
									<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z' />
								</svg>
								Share
							</Button>
						</div>
					</div>
				</div>

				{/* Code/Preview Panel Content */}
				<div className='flex-1 overflow-hidden'>
					{codePreviewPanel}
				</div>
			</div>
		</div>
	);
};
