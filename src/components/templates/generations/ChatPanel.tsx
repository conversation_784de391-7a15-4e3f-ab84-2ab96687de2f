import { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Badge } from '@/components/atoms';
import { PromptInput } from '@/components/molecules';
import {
	UserIcon,
	SparklesIcon,
	ClockIcon,
	ArrowPathIcon,
	TagIcon,
} from '@heroicons/react/24/outline';

interface ChatMessage {
	id: string;
	type: 'user' | 'assistant';
	content: string;
	timestamp: Date;
	isGenerating?: boolean;
	metadata?: any;
}

interface GenerationData {
	id: string;
	type: 'UI' | 'DOCUMENTATION';
	prompt: string;
	status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
	result?: string;
	metadata?: any;
	createdAt: string;
	updatedAt: string;
	project: {
		id: string;
		name: string;
	};
	createdBy: {
		id: string;
		email: string;
		fullName?: string;
	};
	promptHistory?: {
		id: string;
		prompt: string;
		description?: string;
		tags: string[];
		usageCount: number;
	};
}

interface ChatPanelProps {
	generation: GenerationData;
	onRegenerateCode: (prompt: string) => void;
}

export const ChatPanel: React.FC<ChatPanelProps> = ({
	generation,
	onRegenerateCode,
}) => {
	const [messages, setMessages] = useState<ChatMessage[]>([
		{
			id: '1',
			type: 'user',
			content: generation.prompt,
			timestamp: new Date(generation.createdAt),
		},
		{
			id: '2',
			type: 'assistant',
			content: `I'll help you create a ${generation.type.toLowerCase()} generation. ${
				generation.status === 'COMPLETED'
					? "I've successfully generated the code based on your requirements. You can view it in the code panel and make modifications as needed."
					: generation.status === 'IN_PROGRESS'
					? "I'm currently working on your request. This may take a few moments."
					: generation.status === 'FAILED'
					? 'I encountered an issue while generating your request. Please try again or modify your prompt.'
					: 'Your request is in the queue and will be processed shortly.'
			}`,
			timestamp: new Date(generation.updatedAt),
			metadata: generation.metadata,
		},
	]);
	const [newMessage, setNewMessage] = useState('');
	const [isGenerating, setIsGenerating] = useState(false);
	const messagesEndRef = useRef<HTMLDivElement>(null);

	const scrollToBottom = () => {
		messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
	};

	useEffect(() => {
		scrollToBottom();
	}, [messages]);

	const handleSendMessage = async (message: string) => {
		if (!message.trim() || isGenerating) return;

		// Add user message
		const userMessage: ChatMessage = {
			id: Date.now().toString(),
			type: 'user',
			content: message,
			timestamp: new Date(),
		};

		setMessages((prev) => [...prev, userMessage]);
		setNewMessage('');
		setIsGenerating(true);

		// Simulate AI response
		setTimeout(() => {
			const assistantMessage: ChatMessage = {
				id: (Date.now() + 1).toString(),
				type: 'assistant',
				content:
					'I understand your request. Let me update the code to incorporate those changes. This will take a moment to process and generate the updated version.',
				timestamp: new Date(),
			};
			setMessages((prev) => [...prev, assistantMessage]);
			setIsGenerating(false);
		}, 2000);
	};

	const formatTime = (date: Date) => {
		return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
	};

	return (
		<div className='flex flex-col h-full'>
			{/* Messages Container */}
			<div className='flex-1 overflow-y-auto p-4 space-y-4'>
				{messages.map((message) => (
					<div
						key={message.id}
						className={`flex gap-3 ${
							message.type === 'user' ? 'justify-end' : 'justify-start'
						}`}>
						{message.type === 'assistant' && (
							<div className='flex-shrink-0 w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center'>
								<SparklesIcon className='w-4 h-4 text-primary' />
							</div>
						)}

						<div
							className={`
							max-w-[80%] rounded-lg px-4 py-3
							${
								message.type === 'user'
									? 'bg-primary text-white ml-auto'
									: 'bg-surface-100 text-foreground'
							}
						`}>
							<Typography
								variant='body-sm'
								className={`leading-relaxed ${
									message.type === 'user' ? 'text-white' : ''
								}`}>
								{message.content}
							</Typography>
							<div
								className={`
								flex items-center gap-1 mt-2 text-xs
								${message.type === 'user' ? 'text-white/70' : 'text-muted'}
							`}>
								<ClockIcon className='w-3 h-3' />
								{formatTime(message.timestamp)}
							</div>
						</div>

						{message.type === 'user' && (
							<div className='flex-shrink-0 w-8 h-8 bg-surface-200 rounded-full flex items-center justify-center'>
								<UserIcon className='w-4 h-4 text-muted' />
							</div>
						)}
					</div>
				))}

				{/* Generating indicator */}
				{isGenerating && (
					<div className='flex gap-3 justify-start'>
						<div className='flex-shrink-0 w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center'>
							<SparklesIcon className='w-4 h-4 text-primary animate-pulse' />
						</div>
						<div className='bg-surface-100 rounded-lg px-4 py-3'>
							<div className='flex items-center gap-2'>
								<div className='flex space-x-1'>
									<div className='w-2 h-2 bg-muted rounded-full animate-bounce'></div>
									<div
										className='w-2 h-2 bg-muted rounded-full animate-bounce'
										style={{ animationDelay: '0.1s' }}></div>
									<div
										className='w-2 h-2 bg-muted rounded-full animate-bounce'
										style={{ animationDelay: '0.2s' }}></div>
								</div>
								<Typography
									variant='caption'
									color='secondary'>
									Generating response...
								</Typography>
							</div>
						</div>
					</div>
				)}

				<div ref={messagesEndRef} />
			</div>

			{/* Generation Info & Actions */}
			{generation.promptHistory && (
				<div className='flex-shrink-0 p-4 border-t border-border-secondary bg-surface-100'>
					<div className='space-y-3'>
						{generation.promptHistory.tags.length > 0 && (
							<div className='flex items-center gap-2'>
								<TagIcon className='w-4 h-4 text-muted' />
								<div className='flex flex-wrap gap-1'>
									{generation.promptHistory.tags.map((tag) => (
										<Badge
											key={tag}
											variant='secondary'
											size='small'
											emphasis='light'
											className='text-xs'>
											{tag}
										</Badge>
									))}
								</div>
							</div>
						)}

						<div className='flex items-center justify-between'>
							<Typography
								variant='caption'
								color='secondary'>
								Used {generation.promptHistory.usageCount} time
								{generation.promptHistory.usageCount !== 1 ? 's' : ''}
							</Typography>
							<Button
								variant='outline'
								size='sm'
								onClick={() => onRegenerateCode(generation.prompt)}
								className='flex items-center gap-2'>
								<ArrowPathIcon className='w-4 h-4' />
								Regenerate
							</Button>
						</div>
					</div>
				</div>
			)}

			{/* Input Area */}
			<div className='flex-shrink-0 p-4 border-t border-border-secondary bg-surface-50'>
				<PromptInput
					value={newMessage}
					onChange={setNewMessage}
					onSubmit={handleSendMessage}
					placeholder='Ask for modifications, improvements, or new features...'
					loading={isGenerating}
					submitButtonText='Send'
					size='small'
					maxLength={1000}
					className='shadow-sm'
					textareaClassName='bg-surface-50'
					buttonClassName='shadow-sm'
				/>
			</div>
		</div>
	);
};
