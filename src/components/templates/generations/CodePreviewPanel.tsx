import { useState } from 'react';
import { Typo<PERSON>, Button } from '@/components/atoms';
import { 
	DocumentDuplicateIcon,
	CheckIcon,
	FolderIcon,
	DocumentIcon
} from '@heroicons/react/24/outline';

interface FileTab {
	id: string;
	name: string;
	type: 'file' | 'folder';
	content?: string;
	language?: string;
}

export const CodePreviewPanel: React.FC = () => {
	const [activeView, setActiveView] = useState<'code' | 'preview'>('preview');
	const [activeFile, setActiveFile] = useState<string>('app.tsx');
	const [copiedFile, setCopiedFile] = useState<string | null>(null);

	// Mock file structure - will be replaced with real data
	const files: FileTab[] = [
		{
			id: 'app.tsx',
			name: 'App.tsx',
			type: 'file',
			language: 'typescript',
			content: `import React from 'react';
import { Dashboard } from './components/Dashboard';
import { ThemeProvider } from './providers/ThemeProvider';

function App() {
  return (
    <ThemeProvider>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Dashboard />
      </div>
    </ThemeProvider>
  );
}

export default App;`
		},
		{
			id: 'dashboard.tsx',
			name: 'Dashboard.tsx',
			type: 'file',
			language: 'typescript',
			content: `import React from 'react';
import { Sidebar } from './Sidebar';
import { MetricsCards } from './MetricsCards';
import { Charts } from './Charts';

export const Dashboard: React.FC = () => {
  return (
    <div className="flex h-screen">
      <Sidebar />
      <main className="flex-1 p-6">
        <h1 className="text-2xl font-bold mb-6">Dashboard</h1>
        <MetricsCards />
        <Charts />
      </main>
    </div>
  );
};`
		},
		{
			id: 'sidebar.tsx',
			name: 'Sidebar.tsx',
			type: 'file',
			language: 'typescript',
			content: `import React from 'react';

export const Sidebar: React.FC = () => {
  return (
    <aside className="w-64 bg-white dark:bg-gray-800 shadow-lg">
      <div className="p-6">
        <h2 className="text-xl font-semibold">Navigation</h2>
        <nav className="mt-6">
          <ul className="space-y-2">
            <li><a href="#" className="block p-2 rounded hover:bg-gray-100">Dashboard</a></li>
            <li><a href="#" className="block p-2 rounded hover:bg-gray-100">Analytics</a></li>
            <li><a href="#" className="block p-2 rounded hover:bg-gray-100">Settings</a></li>
          </ul>
        </nav>
      </div>
    </aside>
  );
};`
		}
	];

	const activeFileData = files.find(file => file.id === activeFile);

	const handleCopyCode = async (fileId: string) => {
		const file = files.find(f => f.id === fileId);
		if (file?.content) {
			try {
				await navigator.clipboard.writeText(file.content);
				setCopiedFile(fileId);
				setTimeout(() => setCopiedFile(null), 2000);
			} catch (err) {
				console.error('Failed to copy code:', err);
			}
		}
	};

	return (
		<div className='flex h-full'>
			{/* File Explorer */}
			<div className='w-64 bg-surface-50 border-r border-border-secondary flex flex-col'>
				<div className='p-3 border-b border-border-secondary'>
					<Typography variant='body-sm' weight='semibold' color='secondary'>
						Files
					</Typography>
				</div>
				<div className='flex-1 overflow-y-auto'>
					<div className='p-2 space-y-1'>
						{files.map((file) => (
							<button
								key={file.id}
								onClick={() => setActiveFile(file.id)}
								className={`
									w-full flex items-center gap-2 px-3 py-2 rounded-lg text-left transition-colors duration-200
									${activeFile === file.id 
										? 'bg-primary/10 text-primary border border-primary/20' 
										: 'hover:bg-surface-100 text-foreground'
									}
								`}>
								{file.type === 'folder' ? (
									<FolderIcon className='w-4 h-4 flex-shrink-0' />
								) : (
									<DocumentIcon className='w-4 h-4 flex-shrink-0' />
								)}
								<Typography variant='body-sm' className='truncate'>
									{file.name}
								</Typography>
							</button>
						))}
					</div>
				</div>
			</div>

			{/* Code/Preview Content */}
			<div className='flex-1 flex flex-col'>
				{/* File Header */}
				{activeFileData && (
					<div className='flex items-center justify-between p-4 border-b border-border-secondary bg-surface-50'>
						<div className='flex items-center gap-2'>
							<DocumentIcon className='w-4 h-4 text-muted' />
							<Typography variant='body-sm' weight='medium'>
								{activeFileData.name}
							</Typography>
						</div>
						<Button
							variant='ghost'
							size='sm'
							onClick={() => handleCopyCode(activeFile)}
							className='flex items-center gap-2'>
							{copiedFile === activeFile ? (
								<>
									<CheckIcon className='w-4 h-4 text-success' />
									<span className='text-success'>Copied!</span>
								</>
							) : (
								<>
									<DocumentDuplicateIcon className='w-4 h-4' />
									Copy
								</>
							)}
						</Button>
					</div>
				)}

				{/* Content Area */}
				<div className='flex-1 overflow-hidden'>
					{activeView === 'code' ? (
						<div className='h-full bg-layout-100'>
							{/* Code Editor Placeholder */}
							<div className='h-full p-4'>
								<div className='h-full bg-surface-50 rounded-lg border border-border-secondary p-4 font-mono text-sm overflow-auto'>
									<pre className='whitespace-pre-wrap text-foreground'>
										{activeFileData?.content || 'Select a file to view its content'}
									</pre>
								</div>
							</div>
						</div>
					) : (
						<div className='h-full bg-white'>
							{/* Preview Placeholder */}
							<div className='h-full flex items-center justify-center'>
								<div className='text-center p-8'>
									<div className='w-16 h-16 bg-surface-100 rounded-lg flex items-center justify-center mx-auto mb-4'>
										<svg className='w-8 h-8 text-muted' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
											<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={1.5} d='M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z' />
										</svg>
									</div>
									<Typography variant='h4' color='secondary' className='mb-2'>
										Preview Coming Soon
									</Typography>
									<Typography variant='body' color='tertiary' className='max-w-md'>
										The live preview will be available once WebContainers integration is added. 
										For now, you can view and copy the generated code.
									</Typography>
								</div>
							</div>
						</div>
					)}
				</div>
			</div>
		</div>
	);
};
