import { useRouter } from 'next/router';
import {
	PencilIcon,
	TrashIcon,
	UserGroupIcon,
} from '@heroicons/react/24/outline';
import {
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>po<PERSON>,
	Card<PERSON>itle,
	CardDescription,
	Badge,
	DateDisplay,
} from '@/components/atoms';

import type { User } from '@/api/UserApi';

export type ProjectStatus = 'INACTIVE' | 'ACTIVE' | 'ARCHIVED';

export interface ProjectMember {
	id: string;
	projectId: string;
	userId: string;
	joinedAt: string;
	user: User;
}

export interface Project {
	id: string;
	name: string;
	description: string;
	status: ProjectStatus;
	createdAt?: string;
	updatedAt?: string;
	createdById?: string;
	createdBy?: User;
	members?: ProjectMember[];
	_count?: {
		generations?: number;
		promptHistory?: number;
	};
}

export const getProjectMemberUsers = (members?: ProjectMember[]): User[] => {
	return members?.map((member) => member.user) || [];
};

export const getProjectMemberCount = (members?: ProjectMember[]): number => {
	return members?.length || 0;
};

interface ProjectsGridProps {
	projects: Project[];
	isLoading?: boolean;
	canManageProjects?: boolean;
	onEditProject?: (project: Project) => void;
	onDeleteProject?: (project: Project) => void;
}

export const getStatusBadge = (status: ProjectStatus) => {
	switch (status) {
		case 'INACTIVE':
			return {
				variant: 'secondary' as const,
				text: 'Inactive',
			};
		case 'ACTIVE':
			return {
				variant: 'success' as const,
				text: 'Active',
			};

		case 'ARCHIVED':
			return {
				variant: 'info' as const,
				text: 'Archived',
			};

		default:
			return {
				variant: 'secondary' as const,
				text: 'Unknown',
			};
	}
};

const ProjectCard = ({
	project,
	canManageProjects,
	onEditProject,
	onDeleteProject,
}: {
	project: Project;
	canManageProjects?: boolean;
	onEditProject?: (project: Project) => void;
	onDeleteProject?: (project: Project) => void;
}) => {
	const router = useRouter();
	const statusBadge = getStatusBadge(project.status);

	const handleViewProject = () => {
		router.push(`/projects/${project.id}`);
	};

	return (
		<Card
			variant='default'
			size='large'
			className='h-full flex flex-col justify-between bg-surface-50 hover:shadow-lg transition-all duration-200 group'>
			<CardHeader className='pb-3'>
				<div className='flex items-start justify-between mb-2 gap-2'>
					<CardTitle className='flex-1 min-w-0 truncate text-lg'>
						{project.name}
					</CardTitle>
					<Badge
						variant={statusBadge.variant}
						size='small'
						emphasis='light'
						className='flex-shrink-0'>
						{statusBadge.text}
					</Badge>
				</div>
				<CardDescription className='line-clamp-2 sm:line-clamp-3 bg-component-50 px-2 py-1 rounded-md'>
					<DateDisplay
						date={project.updatedAt}
						format='project'
						prefix='Updated: '
						variant='body-sm'
						color='secondary'
						showTooltip
					/>
				</CardDescription>
			</CardHeader>
			<CardContent className='py-4'>
				<Typography
					variant='body-sm'
					color='secondary'
					className='line-clamp-2 mb-4 leading-relaxed'>
					{project.description}
				</Typography>

				{/* Project Stats */}
				<div className='flex items-center justify-between bg-layout-100 px-3 py-2 rounded-lg'>
					{getProjectMemberCount(project.members) > 0 && (
						<div className='flex items-center gap-2'>
							<UserGroupIcon className='w-4 h-4 text-accent-teal-600' />
							<Typography
								variant='caption'
								color='secondary'>
								{getProjectMemberCount(project.members)} member
								{getProjectMemberCount(project.members) !== 1 ? 's' : ''}
							</Typography>
						</div>
					)}
					{project._count && (
						<div className='flex items-center gap-3'>
							{project._count.generations !== undefined && (
								<Typography
									variant='caption'
									color='tertiary'>
									{project._count.generations} gen
									{project._count.generations !== 1 ? 's' : ''}
								</Typography>
							)}
						</div>
					)}
				</div>
			</CardContent>
			<CardFooter className='pt-4 bg-component-50'>
				<div className='flex flex-wrap gap-2'>
					<Button
						variant='primary'
						size='sm'
						onClick={handleViewProject}
						className='flex-1 sm:flex-none min-w-0 shadow-sm'>
						View Project
					</Button>
					{canManageProjects && (
						<div className='flex gap-2'>
							<Button
								variant='secondary'
								size='sm'
								onClick={() => onEditProject?.(project)}
								aria-label={`Edit ${project.name}`}
								className='flex-shrink-0'>
								<PencilIcon className='w-4 h-4' />
								<span className='sr-only'>Edit</span>
							</Button>
							<Button
								variant='danger'
								size='sm'
								onClick={() => onDeleteProject?.(project)}
								aria-label={`Delete ${project.name}`}
								className='flex-shrink-0'>
								<TrashIcon className='w-4 h-4' />
								<span className='sr-only'>Delete</span>
							</Button>
						</div>
					)}
				</div>
			</CardFooter>
		</Card>
	);
};

ProjectCard.displayName = 'ProjectCard';

const ProjectsGrid = ({
	projects,
	isLoading = false,
	canManageProjects = false,
	onEditProject,
	onDeleteProject,
}: ProjectsGridProps) => {
	if (isLoading) {
		return (
			<div className='flex items-center justify-center py-16'>
				<div className='text-center'>
					<div className='animate-spin rounded-full h-12 w-12 border-2 border-border-secondary border-t-primary mx-auto mb-4'></div>
					<Typography
						variant='body'
						color='secondary'>
						Loading projects...
					</Typography>
				</div>
			</div>
		);
	}

	if (projects.length === 0) {
		return (
			<div className='flex flex-col items-center justify-center py-16 sm:py-20 px-4'>
				<div className='bg-surface-50 rounded-2xl p-8 shadow-sm text-center max-w-md'>
					<div className='w-16 h-16 bg-component-100 rounded-full flex items-center justify-center mx-auto mb-4'>
						<svg
							className='w-8 h-8 text-muted'
							fill='none'
							stroke='currentColor'
							viewBox='0 0 24 24'>
							<path
								strokeLinecap='round'
								strokeLinejoin='round'
								strokeWidth={1.5}
								d='M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10'
							/>
						</svg>
					</div>
					<Typography
						variant='h4'
						color='secondary'
						className='mb-3'>
						No projects yet
					</Typography>
					<Typography
						variant='body'
						color='tertiary'
						className='leading-relaxed'>
						Create your first project to get started with organizing your
						creative work
					</Typography>
				</div>
			</div>
		);
	}

	return (
		<div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 sm:gap-6'>
			{projects.map((project) => (
				<ProjectCard
					key={project.id}
					project={project}
					canManageProjects={canManageProjects}
					onEditProject={onEditProject}
					onDeleteProject={onDeleteProject}
				/>
			))}
		</div>
	);
};

ProjectsGrid.displayName = 'ProjectsGrid';

export default ProjectsGrid;
