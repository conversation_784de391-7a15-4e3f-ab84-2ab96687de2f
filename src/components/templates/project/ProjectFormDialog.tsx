import { useState, useEffect } from 'react';
import { Field, Label, Fieldset } from '@headlessui/react';

import {
	Input,
	Textarea,
	Button,
	Alert,
	<PERSON>ton<PERSON>oa<PERSON>,
	Typography,
} from '@/components/atoms';
import { Dialog, MemberSelect } from '@/components/molecules';
import { useProjects } from '@/hooks/useProjects';
import type { User } from '@/api/UserApi';
import type { Project } from './ProjectsGrid';
import { getProjectMemberUsers } from './ProjectsGrid';

interface ProjectFormData {
	name: string;
	description: string;
	members?: User[];
}

interface ProjectFormDialogProps {
	isOpen: boolean;
	onClose: () => void;
	mode: 'create' | 'edit';
	project?: Project | null;
	onProjectCreated?: (
		projectData: ProjectFormData,
	) => Promise<{ id: string; name: string; description: string } | null>;
	onProjectUpdated?: (
		id: string,
		projectData: ProjectFormData,
	) => Promise<Project | null>;
}

const ProjectFormDialog: React.FC<ProjectFormDialogProps> = ({
	isOpen,
	onClose,
	mode,
	project,
	onProjectCreated,
	onProjectUpdated,
}) => {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [selectedMembers, setSelectedMembers] = useState<User[]>([]);

	const {
		createProject,
		updateProject,
		addProjectMembers,
		removeProjectMembers,
	} = useProjects();

	useEffect(() => {
		if (isOpen) {
			setError(null);
			if (mode === 'edit' && project?.members) {
				setSelectedMembers(getProjectMemberUsers(project.members));
			} else {
				setSelectedMembers([]);
			}
		}
	}, [isOpen, project, mode]);

	const getDialogContent = () => {
		if (mode === 'create') {
			return {
				title: 'Create New Project',
				description: 'Create a new project to organize your creative work.',
				submitText: 'Create Project',
				submittingText: 'Creating...',
			};
		}
		return {
			title: 'Edit Project',
			description: 'Update your project details.',
			submitText: 'Update Project',
			submittingText: 'Updating...',
		};
	};

	const dialogContent = getDialogContent();

	const handleMemberChanges = async (
		projectId: string,
		newMembers: User[],
		originalMembers: User[] = [],
	) => {
		const newMemberIds = newMembers.map((m) => m.id);
		const originalMemberIds = originalMembers.map((m) => m.id);

		const membersToAdd = newMemberIds.filter(
			(id) => !originalMemberIds.includes(id),
		);

		const membersToRemove = originalMemberIds.filter(
			(id) => !newMemberIds.includes(id),
		);

		if (membersToAdd.length > 0) {
			const addResult = await addProjectMembers(projectId, membersToAdd);
			if (!addResult) {
				throw new Error('Failed to add members');
			}
		}


		if (membersToRemove.length > 0) {
			const removeResult = await removeProjectMembers(
				projectId,
				membersToRemove,
			);
			if (!removeResult) {
				throw new Error('Failed to remove members');
			}
		}
	};

	const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
		event.preventDefault();
		setError(null);
		setIsSubmitting(true);

		const formData = new FormData(event.currentTarget);
		const name = formData.get('name') as string;
		const description = formData.get('description') as string;

		if (!name.trim()) {
			setError('Project name is required');
			setIsSubmitting(false);
			return;
		}

		if (!description.trim()) {
			setError('Project description is required');
			setIsSubmitting(false);
			return;
		}

		const projectData: ProjectFormData = {
			name: name.trim(),
			description: description.trim(),
			members: selectedMembers,
		};

		try {
			if (mode === 'create') {
				await handleCreateProject(event, projectData);
			} else {
				await handleUpdateProject(projectData);
			}
		} catch (error) {
			console.error(
				`Error ${mode === 'create' ? 'creating' : 'updating'} project:`,
				error,
			);
			if (error instanceof Error) {
				setError(`Failed to ${mode} project: ${error.message}`);
			} else {
				setError(
					`Failed to ${mode} project: Network error or server unavailable`,
				);
			}
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleCreateProject = async (
		event: React.FormEvent<HTMLFormElement>,
		projectData: ProjectFormData,
	) => {
		const basicProjectData = {
			name: projectData.name,
			description: projectData.description,
		};

		let createdProject;
		if (onProjectCreated) {
			createdProject = await onProjectCreated(basicProjectData);
		} else {
			createdProject = await createProject(basicProjectData);
		}

		if (!createdProject) {
			setError('Failed to create project');
			return;
		}

		if (selectedMembers.length > 0) {
			try {
				const memberIds = selectedMembers.map((m) => m.id);
				await addProjectMembers(createdProject.id, memberIds);
			} catch (error) {
				setError(
					error instanceof Error
						? error.message
						: 'Project created but failed to add members',
				);
				return;
			}
		}

		(event.target as HTMLFormElement).reset();
		onClose();
	};

	const handleUpdateProject = async (projectData: ProjectFormData) => {
		if (!project) {
			setError('No project selected for editing');
			return;
		}

		const basicProjectData = {
			name: projectData.name,
			description: projectData.description,
		};

		let updatedProject;
		if (onProjectUpdated) {
			updatedProject = await onProjectUpdated(project.id, basicProjectData);
		} else {
			updatedProject = await updateProject(project.id, basicProjectData);
		}

		if (!updatedProject) {
			setError('Failed to update project');
			return;
		}

		try {
			await handleMemberChanges(
				project.id,
				selectedMembers,
				getProjectMemberUsers(project.members),
			);
			onClose();
		} catch (error) {
			setError(
				error instanceof Error ? error.message : 'Failed to update members',
			);
		}
	};

	const handleClose = () => {
		if (!isSubmitting) {
			setError(null);
			onClose();
		}
	};

	return (
		<Dialog
			isOpen={isOpen}
			onClose={handleClose}
			title={dialogContent.title}
			description={dialogContent.description}
			size='lg'>
			{error && (
				<Alert
					variant='negative'
					className='mb-3'>
					{error}
				</Alert>
			)}

			<form
				onSubmit={handleSubmit}
				className='space-y-3'>
				<Fieldset className='space-y-3'>
					<Field>
						<Label htmlFor='name'>
							<Typography
								variant='body-sm'
								weight='medium'
								color='secondary'>
								Project Name
							</Typography>
						</Label>
						<Input
							id='name'
							name='name'
							type='text'
							required
							placeholder='Enter project name'
							defaultValue={mode === 'edit' ? project?.name || '' : ''}
							className='mt-1'
							disabled={isSubmitting}
						/>
					</Field>

					<Field>
						<Label htmlFor='description'>
							<Typography
								variant='body-sm'
								weight='medium'
								color='secondary'>
								Description
							</Typography>
						</Label>
						<Textarea
							id='description'
							name='description'
							rows={4}
							required
							placeholder='Describe your project...'
							defaultValue={mode === 'edit' ? project?.description || '' : ''}
							disabled={isSubmitting}
							className='mt-1'
						/>
					</Field>

					<MemberSelect
						value={selectedMembers}
						onChange={setSelectedMembers}
						disabled={isSubmitting}
						className='mt-1'
					/>
				</Fieldset>

				<div className='flex flex-col sm:flex-row justify-end gap-1.5 pt-2 border-t border-border'>
					<Button
						type='button'
						variant='secondary'
						onClick={handleClose}
						disabled={isSubmitting}
						className='order-2 sm:order-1'>
						Cancel
					</Button>
					<Button
						type='submit'
						variant='primary'
						disabled={isSubmitting}
						className='order-1 sm:order-2'>
						{isSubmitting && <ButtonLoader />}
						{isSubmitting
							? dialogContent.submittingText
							: dialogContent.submitText}
					</Button>
				</div>
			</form>
		</Dialog>
	);
};

export default ProjectFormDialog;
export type { ProjectFormData, ProjectFormDialogProps };
