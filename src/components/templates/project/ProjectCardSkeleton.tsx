import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>ontent, CardFooter } from '@/components/atoms';

const ProjectCardSkeleton = () => (
	<Card
		variant='outlined'
		size='medium'
		className='h-full animate-pulse'>
		<CardHeader>
			{/* Title skeleton */}
			<div className='h-6 bg-gray-200 dark:bg-gray-700 rounded mb-2'></div>
			{/* Description skeleton */}
			<div className='h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4'></div>
		</CardHeader>
		<CardContent>
			{/* Created date skeleton */}
			<div className='h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2 w-1/2'></div>
			{/* Updated date skeleton */}
			<div className='h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2'></div>
		</CardContent>
		<CardFooter>
			{/* Button skeleton */}
			<div className='h-8 bg-gray-200 dark:bg-gray-700 rounded w-16'></div>
		</CardFooter>
	</Card>
);

ProjectCardSkeleton.displayName = 'ProjectCardSkeleton';

export default ProjectCardSkeleton;
