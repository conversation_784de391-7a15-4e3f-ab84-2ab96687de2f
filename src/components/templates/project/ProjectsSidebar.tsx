import React from 'react';
import {
	FolderIcon,
	PlusIcon,
	ArchiveBoxIcon,
	PlayIcon,
} from '@heroicons/react/24/outline';
import SideBar from '@/components/organisms/Sidebar';
import { Badge } from '@/components/atoms';
import type { Project, ProjectStatus } from './ProjectsGrid';

interface ProjectsSidebarProps {
	projects: Project[];
	isCollapsed: boolean;
	onToggle: () => void;
	onProjectClick?: (project: Project) => void;
	onCreateProject?: () => void;
	activeProjectId?: string;
	canManageProjects?: boolean;
}

const getStatusIcon = (status: ProjectStatus) => {
	switch (status) {
		case 'INACTIVE':
			return <FolderIcon className='w-4 h-4' />;
		case 'ACTIVE':
			return <PlayIcon className='w-4 h-4' />;
		default:
			return <ArchiveBoxIcon className='w-4 h-4' />;
	}
};

const getStatusBadge = (status: ProjectStatus, isCollapsed: boolean) => {
	if (isCollapsed) return null;

	const badgeConfig = {
		INACTIVE: { variant: 'secondary' as const, text: 'Inactive' },
		ACTIVE: { variant: 'success' as const, text: 'Active' },
		ARCHIVED: { variant: 'info' as const, text: 'Archived' },
	};

	const config = badgeConfig[status];
	return (
		<Badge
			variant={config.variant}
			size='small'
			emphasis='light'>
			{config.text}
		</Badge>
	);
};

const ProjectsSidebar: React.FC<ProjectsSidebarProps> = ({
	projects,
	isCollapsed,
	onToggle,
	onProjectClick,
	onCreateProject,
	activeProjectId,
	canManageProjects = false,
}) => {
	const projectItems = projects.map((project) => ({
		label: project.name,
		icon: getStatusIcon(project.status),
		badge: getStatusBadge(project.status, isCollapsed),
		isActive: activeProjectId === project.id,
		onClick: () => onProjectClick?.(project),
	}));

	const newProjectItem = canManageProjects
		? {
				label: 'New Project',
				icon: <PlusIcon className='w-4 h-4' />,
				onClick: onCreateProject,
		  }
		: null;

	const sidebarSections = [];

	if (newProjectItem) {
		sidebarSections.push({
			title: 'Actions',
			items: [newProjectItem],
		});
	}

	if (projectItems.length > 0) {
		sidebarSections.push({
			title: `My Projects (${projects.length})`,
			items: projectItems,
		});
	} else if (!canManageProjects) {
		sidebarSections.push({
			title: 'Projects',
			items: [
				{
					label: 'No projects available',
					icon: <FolderIcon className='w-4 h-4' />,
					disabled: true,
					onClick: () => {},
				},
			],
		});
	}

	return (
		<SideBar
			title='Projects'
			subtitle={`${projects.length} project${projects.length !== 1 ? 's' : ''}`}
			sections={sidebarSections}
			isCollapsed={isCollapsed}
			onToggle={onToggle}
		/>
	);
};

ProjectsSidebar.displayName = 'ProjectsSidebar';

export default ProjectsSidebar;
