import { PlusIcon } from '@heroicons/react/24/outline';
import { Button, Typography } from '@/components/atoms';
import ThemeToggle from '@/components/molecules/ThemeToggle';

interface ProjectsHeaderProps {
	onCreateProject: () => void;
	canManageProjects?: boolean;
}

const ProjectsHeader = ({
	onCreateProject,
	canManageProjects = false,
}: ProjectsHeaderProps) => {
	return (
		<div className='mb-6 sm:mb-8'>
			<div className='bg-surface-50 rounded-xl shadow-sm p-6'>
				<div className='flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 sm:gap-6'>
					<div className='flex-1 min-w-0'>
						<Typography
							variant='h1'
							responsive
							className='mb-2'>
							Projects
						</Typography>
						<Typography
							variant='body-lg'
							color='secondary'
							className='hidden sm:block'>
							Manage and track your creative projects with our enhanced design
							system.
						</Typography>
						<Typography
							variant='body'
							color='secondary'
							className='sm:hidden'>
							Manage and track your projects.
						</Typography>
					</div>
					<div className='flex items-center justify-between sm:justify-end gap-3 flex-shrink-0'>
						<div className='p-1 bg-component-50 rounded-lg'>
							<ThemeToggle />
						</div>
						{canManageProjects && (
							<Button
								variant='primary'
								size='md'
								onClick={onCreateProject}
								className='flex-shrink-0 shadow-sm'>
								<PlusIcon className='w-5 h-5 mr-2' />
								<span className='hidden sm:inline'>New Project</span>
								<span className='sm:hidden'>New</span>
							</Button>
						)}
					</div>
				</div>
			</div>
		</div>
	);
};

ProjectsHeader.displayName = 'ProjectsHeader';

export default ProjectsHeader;
