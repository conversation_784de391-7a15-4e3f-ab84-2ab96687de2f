import { memo } from 'react';
import { PlusIcon } from '@heroicons/react/24/outline';
import { Button, Typography } from '@/components/atoms';
import ThemeToggle from '@/components/molecules/ThemeToggle';

interface ProjectsHeaderProps {
	onCreateProject: () => void;
	canManageProjects?: boolean;
}

const ProjectsHeader = memo<ProjectsHeaderProps>(
	({ onCreateProject, canManageProjects = false }) => {
		return (
			<div className='mb-2 sm:mb-3'>
				<div className='flex flex-col sm:flex-row sm:justify-between sm:items-start gap-1 sm:gap-1.5 mb-1'>
					<div className='flex-1 min-w-0'>
						<Typography
							variant='h1'
							responsive
							className='mb-1'>
							Projects
						</Typography>
						<Typography
							variant='body-lg'
							color='secondary'
							className='hidden sm:block'>
							Manage and track your creative projects with our enhanced design
							system.
						</Typography>
						<Typography
							variant='body'
							color='secondary'
							className='sm:hidden'>
							Manage and track your projects.
						</Typography>
					</div>
					<div className='flex items-center justify-between sm:justify-end gap-1.5 flex-shrink-0'>
						<ThemeToggle />
						{canManageProjects && (
							<Button
								variant='primary'
								size='md'
								onClick={onCreateProject}
								className='flex-shrink-0'>
								<PlusIcon className='w-4 h-4 mr-2' />
								<span className='hidden sm:inline'>New Project</span>
								<span className='sm:hidden'>New</span>
							</Button>
						)}
					</div>
				</div>
			</div>
		);
	},
);

ProjectsHeader.displayName = 'ProjectsHeader';

export default ProjectsHeader;
