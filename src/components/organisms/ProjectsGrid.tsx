import { useRouter } from 'next/router';
import {
	PencilIcon,
	TrashIcon,
	UserGroupIcon,
} from '@heroicons/react/24/outline';
import {
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>po<PERSON>,
	Card<PERSON>itle,
	CardDescription,
	Badge,
	DateDisplay,
} from '@/components/atoms';
import ProjectCardSkeleton from '@/components/templates/project/ProjectCardSkeleton';
import type { User } from '@/api/UserApi';

export type ProjectStatus = 'INACTIVE' | 'ACTIVE' | 'ARCHIVED';

export interface ProjectMember {
	id: string;
	projectId: string;
	userId: string;
	joinedAt: string;
	user: User;
}

export interface Project {
	id: string;
	name: string;
	description: string;
	status: ProjectStatus;
	createdAt?: string;
	updatedAt?: string;
	createdById?: string;
	createdBy?: User;
	members?: ProjectMember[];
	_count?: {
		generations?: number;
		promptHistory?: number;
	};
}

// Utility function to extract users from project members
export const getProjectMemberUsers = (members?: ProjectMember[]): User[] => {
	return members?.map((member) => member.user) || [];
};

// Utility function to get member count
export const getProjectMemberCount = (members?: ProjectMember[]): number => {
	return members?.length || 0;
};

interface ProjectsGridProps {
	projects: Project[];
	isLoading?: boolean;
	canManageProjects?: boolean;
	onEditProject?: (project: Project) => void;
	onDeleteProject?: (project: Project) => void;
}

export const getStatusBadge = (status: ProjectStatus) => {
	switch (status) {
		case 'INACTIVE':
			return {
				variant: 'secondary' as const,
				text: 'Inactive',
			};
		case 'ACTIVE':
			return {
				variant: 'success' as const,
				text: 'Active',
			};

		case 'ARCHIVED':
			return {
				variant: 'info' as const,
				text: 'Archived',
			};

		default:
			return {
				variant: 'secondary' as const,
				text: 'Unknown',
			};
	}
};

const ProjectCard = ({
	project,
	canManageProjects,
	onEditProject,
	onDeleteProject,
}: {
	project: Project;
	canManageProjects?: boolean;
	onEditProject?: (project: Project) => void;
	onDeleteProject?: (project: Project) => void;
}) => {
	const router = useRouter();
	const statusBadge = getStatusBadge(project.status);

	const handleViewProject = () => {
		router.push(`/projects/${project.id}`);
	};

	return (
		<Card
			variant='default'
			size='large'
			className='h-full flex flex-col justify-between'>
			<CardHeader>
				<div className='flex items-start justify-between mb-1 gap-1'>
					<CardTitle className='flex-1 min-w-0 truncate'>
						{project.name}
					</CardTitle>
					<Badge
						variant={statusBadge.variant}
						size='small'
						emphasis='light'
						className='flex-shrink-0'>
						{statusBadge.text}
					</Badge>
				</div>
				<CardDescription className='line-clamp-2 sm:line-clamp-3'>
					<DateDisplay
						date={project.updatedAt}
						format='project'
						prefix='Updated: '
						variant='body-sm'
						color='secondary'
						showTooltip
					/>
				</CardDescription>
			</CardHeader>
			<CardContent>
				<Typography
					variant='body-sm'
					color='secondary'
					className='line-clamp-2 mb-3'>
					{project.description}
				</Typography>

				{/* Project Stats */}
				<div className='flex items-center justify-between text-xs text-muted mb-2'>
					{getProjectMemberCount(project.members) > 0 && (
						<div className='flex items-center gap-1'>
							<UserGroupIcon className='w-3 h-3' />
							<span>
								{getProjectMemberCount(project.members)} member
								{getProjectMemberCount(project.members) !== 1 ? 's' : ''}
							</span>
						</div>
					)}
					{project._count && (
						<div className='flex items-center gap-2'>
							{project._count.generations !== undefined && (
								<span>
									{project._count.generations} gen
									{project._count.generations !== 1 ? 's' : ''}
								</span>
							)}
						</div>
					)}
				</div>
			</CardContent>
			<CardFooter>
				<div className='flex flex-wrap gap-1 sm:gap-1.5'>
					<Button
						variant='primary'
						size='sm'
						onClick={handleViewProject}
						className='flex-1 sm:flex-none min-w-0'>
						View
					</Button>
					{canManageProjects && (
						<>
							<Button
								variant='secondary'
								size='sm'
								onClick={() => onEditProject?.(project)}
								aria-label={`Edit ${project.name}`}
								className='flex-shrink-0'>
								<PencilIcon className='w-4 h-4' />
								<span className='sr-only'>Edit</span>
							</Button>
							<Button
								variant='danger'
								size='sm'
								onClick={() => onDeleteProject?.(project)}
								aria-label={`Delete ${project.name}`}
								className='flex-shrink-0'>
								<TrashIcon className='w-4 h-4' />
								<span className='sr-only'>Delete</span>
							</Button>
						</>
					)}
				</div>
			</CardFooter>
		</Card>
	);
};

ProjectCard.displayName = 'ProjectCard';

const ProjectsGrid = ({
	projects,
	isLoading = false,
	canManageProjects = false,
	onEditProject,
	onDeleteProject,
}: ProjectsGridProps) => {
	if (isLoading) {
		return (
			<div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-2 sm:gap-3 mb-4'>
				{Array.from({ length: 6 }).map((_, index) => (
					<ProjectCardSkeleton key={index} />
				))}
			</div>
		);
	}

	if (projects.length === 0) {
		return (
			<div className='flex flex-col items-center justify-center py-8 sm:py-12 px-2'>
				<Typography
					variant='h3'
					color='secondary'
					className='mb-2 text-center'>
					No projects yet
				</Typography>
				<Typography
					variant='body'
					color='tertiary'
					className='text-center max-w-md'>
					Create your first project to get started
				</Typography>
			</div>
		);
	}

	return (
		<div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-2 sm:gap-3 mb-4'>
			{projects.map((project) => (
				<ProjectCard
					key={project.id}
					project={project}
					canManageProjects={canManageProjects}
					onEditProject={onEditProject}
					onDeleteProject={onDeleteProject}
				/>
			))}
		</div>
	);
};

ProjectsGrid.displayName = 'ProjectsGrid';

export default ProjectsGrid;
