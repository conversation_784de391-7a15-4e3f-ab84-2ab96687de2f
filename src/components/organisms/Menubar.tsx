import Link from 'next/link';
import { useState } from 'react';
import { Button, Typography } from '@/components/atoms';
import { useAuthStore } from '@/providers/auth-store-provider';
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';

interface MenubarProps {
	logo: React.ReactNode;
	links: {
		title: string;
		href: string;
		startIcon?: React.ReactNode;
		endIcon?: React.ReactNode;
		isActive?: boolean;
		onClick?: () => void;
		className?: string;
		disabled?: boolean;
	}[];
}

const Menubar: React.FC<MenubarProps> = ({ logo, links }) => {
	const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
	const user = useAuthStore((state) => state.user);
	const logout = useAuthStore((state) => state.logout);
	const isLoading = useAuthStore((state) => state.isLoading);

	const toggleMobileMenu = () => {
		setIsMobileMenuOpen(!isMobileMenuOpen);
	};

	const closeMobileMenu = () => {
		setIsMobileMenuOpen(false);
	};

	return (
		<header className='bg-surface-50 border-b border-border-secondary shadow-sm relative backdrop-blur-sm'>
			{/* Desktop Header */}
			<div className='flex items-center justify-between h-16 px-4'>
				{/* Logo */}
				<div className='flex-shrink-0'>
					<Typography
						variant='h5'
						weight='semibold'>
						{logo}
					</Typography>
				</div>

				{/* Desktop Navigation */}
				<nav className='hidden md:flex items-center gap-2'>
					{links.map((link, index) => (
						<Link
							key={index}
							href={link.href}
							className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${
								link.isActive
									? 'bg-component-100 text-primary border border-primary-200 shadow-sm'
									: 'text-muted hover:text-foreground hover:bg-component-50 border border-transparent hover:border-border-secondary'
							} ${link.className}`}
							onClick={link.onClick}
							aria-disabled={link.disabled}>
							{link.startIcon && (
								<span className='flex-shrink-0 w-5 h-5'>{link.startIcon}</span>
							)}
							<Typography
								variant='body-sm'
								weight='medium'>
								{link.title}
							</Typography>
							{link.endIcon && (
								<span className='flex-shrink-0 w-5 h-5'>{link.endIcon}</span>
							)}
						</Link>
					))}
				</nav>

				{/* Desktop User Menu */}
				{user && (
					<div className='hidden md:flex items-center gap-3 px-2 py-1 rounded-lg bg-layout-100 border border-border-muted'>
						<Typography
							variant='body-sm'
							color='secondary'
							className='hidden lg:block'>
							{user.fullName}
						</Typography>
						<Button
							variant='ghost'
							size='sm'
							onClick={logout}
							disabled={isLoading}
							className='text-xs'>
							{isLoading ? 'Logging out...' : 'Logout'}
						</Button>
					</div>
				)}

				{/* Mobile Menu Button */}
				<div className='md:hidden flex items-center gap-2'>
					{user && (
						<Button
							variant='ghost'
							size='sm'
							onClick={logout}
							disabled={isLoading}
							className='text-xs px-2 py-1'>
							{isLoading ? 'Logging out...' : 'Logout'}
						</Button>
					)}
					<button
						onClick={toggleMobileMenu}
						className='p-2 rounded-lg hover:bg-component-50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-surface-50 transition-colors duration-200'
						aria-label='Toggle mobile menu'
						aria-expanded={isMobileMenuOpen}>
						{isMobileMenuOpen ? (
							<XMarkIcon className='w-6 h-6 text-muted' />
						) : (
							<Bars3Icon className='w-6 h-6 text-muted' />
						)}
					</button>
				</div>
			</div>

			{/* Mobile Navigation Menu */}
			{isMobileMenuOpen && (
				<div className='md:hidden absolute top-full left-0 right-0 bg-surface-100 border-b border-border-secondary shadow-xl z-50 backdrop-blur-sm'>
					<nav className='px-4 py-3 space-y-2'>
						{links.map((link, index) => (
							<Link
								key={index}
								href={link.href}
								className={`flex items-center gap-3 px-3 py-2.5 rounded-lg transition-all duration-200 w-full ${
									link.isActive
										? 'bg-component-200 text-primary border border-primary-200 shadow-sm'
										: 'text-muted hover:text-foreground hover:bg-component-100 border border-transparent hover:border-border-secondary'
								} ${link.className}`}
								onClick={() => {
									link.onClick?.();
									closeMobileMenu();
								}}
								aria-disabled={link.disabled}>
								{link.startIcon && (
									<span className='flex-shrink-0 w-5 h-5'>
										{link.startIcon}
									</span>
								)}
								<Typography
									variant='body-sm'
									weight='medium'
									className='flex-1'>
									{link.title}
								</Typography>
								{link.endIcon && (
									<span className='flex-shrink-0 w-5 h-5'>{link.endIcon}</span>
								)}
							</Link>
						))}

						{/* Mobile User Info */}
						{user && (
							<div className='pt-3 mt-3 border-t border-border-muted'>
								<div className='px-3 py-2 bg-layout-100 rounded-lg border border-border-secondary'>
									<Typography
										variant='caption'
										color='tertiary'
										className='uppercase tracking-wide'>
										Signed in as
									</Typography>
									<Typography
										variant='body-sm'
										weight='medium'
										className='mt-1'>
										{user.fullName}
									</Typography>
								</div>
							</div>
						)}
					</nav>
				</div>
			)}

			{/* Mobile Menu Backdrop */}
			{isMobileMenuOpen && (
				<div
					className='md:hidden fixed inset-0 bg-neutral-900/30 backdrop-blur-sm z-40'
					onClick={closeMobileMenu}
				/>
			)}
		</header>
	);
};

Menubar.displayName = 'Menubar';
export default Menubar;
