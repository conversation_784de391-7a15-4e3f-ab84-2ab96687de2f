import { Button as HeadlessButton } from '@headlessui/react';
import { forwardRef } from 'react';
import { clsx } from 'clsx';

interface ButtonProps
	extends React.ComponentProps<typeof HeadlessButton>,
		Omit<
			React.ButtonHTMLAttributes<HTMLButtonElement>,
			keyof React.ComponentProps<typeof HeadlessButton>
		> {
	className?: string;
	children: React.ReactNode;
	variant?: 'primary' | 'secondary' | 'outline' | 'link' | 'ghost' | 'danger';
	size?: 'sm' | 'md' | 'lg';
	icon?: React.ReactNode;
}

const buttonVariants = {
	primary: [
		'bg-primary text-white',
		'hover:bg-primary-hover',
		'active:bg-primary-active',
		'focus:ring-primary',
		'disabled:bg-disabled disabled:text-muted',
	],
	secondary: [
		'bg-secondary text-foreground',
		'hover:bg-secondary-hover',
		'active:bg-secondary-active',
		'focus:ring-primary',
		'disabled:bg-disabled disabled:text-muted disabled:border-border-secondary',
	],
	outline: [
		'bg-transparent text-primary border border-border',
		'hover:bg-primary/8 hover:text-primary-hover hover:border-primary',
		'active:bg-primary/15 active:text-primary-active',
		'focus:ring-primary',
		'disabled:border-border-secondary disabled:text-muted-foreground',
	],
	link: [
		'bg-transparent text-primary underline underline-offset-2',
		'hover:text-primary-hover hover:cursor-pointer',
		'active:text-primary-active',
		'focus:text-primary-focus',
		'disabled:text-muted-foreground disabled:no-underline',
	],
	ghost: [
		'bg-transparent text-muted-foreground',
		'hover:bg-secondary hover:text-foreground',
		'active:bg-secondary-active active:text-foreground',
		'focus:ring-primary',
		'disabled:text-muted',
	],
	danger: [
		'bg-danger text-white',
		'hover:bg-danger-hover',
		'active:bg-danger-active',
		'focus:ring-danger',
		'disabled:bg-disabled disabled:text-muted',
	],
};

const buttonSizes = {
	sm: 'px-3 py-1.5 text-sm h-8',
	md: 'px-4 py-2 text-base h-10',
	lg: 'px-6 py-3 text-lg h-12',
};

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
	(
		{ className, children, variant = 'primary', size = 'md', icon, ...props },
		ref,
	) => {
		return (
			<HeadlessButton
				ref={ref}
				className={clsx(
					'inline-flex items-center justify-center',
					'rounded-md transition-all duration-200 ease-in-out',
					'focus:outline-none focus:ring-2 focus:ring-offset-2',
					'focus:ring-offset-surface',
					'disabled:opacity-50 disabled:cursor-not-allowed',
					buttonVariants[variant],
					buttonSizes[size],
					className,
				)}
				{...props}>
				{icon && <span className='mr-1'>{icon}</span>}
				{children}
			</HeadlessButton>
		);
	},
);

Button.displayName = 'Button';

export default Button;
