import { Textarea as HeadlessTextarea } from '@headlessui/react';
import { forwardRef } from 'react';
import { clsx } from 'clsx';

interface TextareaProps
	extends React.ComponentProps<typeof HeadlessTextarea>,
		Omit<
			React.TextareaHTMLAttributes<HTMLTextAreaElement>,
			keyof React.ComponentProps<typeof HeadlessTextarea> | 'size'
		> {
	className?: string;
	textareaSize?: 'small' | 'medium' | 'large';
	variant?: 'default' | 'error';
}

const textareaSizes = {
	small:
		'px-[calc(var(--spacing-xs)*0.8)] py-[calc(var(--spacing-xs)*0.4)] text-[var(--text-sm)] min-h-[5rem]',
	medium:
		'px-[var(--spacing-sm)] py-[calc(var(--spacing-sm)*0.67)] text-[var(--text-md)] min-h-[6rem]',
	large:
		'px-[var(--spacing-md)] py-[calc(var(--spacing-md)*0.75)] text-[var(--text-lg)] min-h-[8rem]',
};

const textareaVariants = {
	default: [
		'bg-surface text-foreground',
		'border border-border-secondary',
		'focus:border-primary focus:ring-1 focus:ring-primary',
		'hover:border-border',
	],
	error: [
		'bg-surface text-foreground',
		'border border-danger-600',
		'focus:border-danger focus:ring-1 focus:ring-danger',
	],
};

const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
	(
		{ className, textareaSize = 'medium', variant = 'default', ...props },
		ref,
	) => {
		return (
			<HeadlessTextarea
				ref={ref}
				className={clsx(
					'block w-full rounded-md resize-vertical',
					'transition-all duration-200 ease-in-out',
					'focus:outline-none',
					'placeholder:text-muted',
					'disabled:cursor-not-allowed disabled:opacity-50',
					'disabled:bg-surface',
					textareaSizes[textareaSize],
					textareaVariants[variant],
					className,
				)}
				{...props}
			/>
		);
	},
);

Textarea.displayName = 'Textarea';
export default Textarea;
