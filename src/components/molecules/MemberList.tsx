import { memo } from 'react';
import { UserIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { Typography, Button } from '@/components/atoms';
import type { User } from '@/api/UserApi';
import Image from 'next/image';

interface MemberListProps {
	members: User[];
	onRemoveMember?: (member: User) => void;
	canManageMembers?: boolean;
	className?: string;
	showRemoveButton?: boolean;
	maxDisplayed?: number;
}

const MemberList: React.FC<MemberListProps> = ({
	members,
	onRemoveMember,
	canManageMembers = false,
	className,
	showRemoveButton = true,
	maxDisplayed,
}) => {
	if (!members || members.length === 0) {
		return (
			<div className={`flex items-center justify-center py-4 ${className || ''}`}>
				<div className='text-center'>
					<UserIcon className='w-8 h-8 text-muted mx-auto mb-2' />
					<Typography
						variant='body-sm'
						color='tertiary'>
						No team members assigned
					</Typography>
				</div>
			</div>
		);
	}

	const displayedMembers = maxDisplayed ? members.slice(0, maxDisplayed) : members;
	const remainingCount = maxDisplayed && members.length > maxDisplayed 
		? members.length - maxDisplayed 
		: 0;

	return (
		<div className={className}>
			<div className='space-y-2'>
				{displayedMembers.map((member) => (
					<div
						key={member.id}
						className='flex items-center justify-between p-2 rounded-md bg-surface-secondary/50 border border-border-secondary'>
						<div className='flex items-center gap-2 flex-1 min-w-0'>
							{member.avatar ? (
								<Image
									src={member.avatar}
									alt={member.fullName || member.email}
									width={32}
									height={32}
									className='w-8 h-8 rounded-full object-cover flex-shrink-0'
								/>
							) : (
								<div className='w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0'>
									<UserIcon className='w-4 h-4 text-primary' />
								</div>
							)}
							<div className='flex-1 min-w-0'>
								<Typography
									variant='body-sm'
									weight='medium'
									className='truncate'>
									{member.fullName || member.email}
								</Typography>
								{member.fullName && member.email && (
									<Typography
										variant='caption'
										color='tertiary'
										className='truncate'>
										{member.email}
									</Typography>
								)}
							</div>
						</div>
						
						{canManageMembers && showRemoveButton && onRemoveMember && (
							<Button
								variant='ghost'
								size='sm'
								onClick={() => onRemoveMember(member)}
								className='flex-shrink-0 text-danger hover:text-danger-600 hover:bg-danger/10'
								aria-label={`Remove ${member.fullName || member.email}`}>
								<XMarkIcon className='w-4 h-4' />
							</Button>
						)}
					</div>
				))}
				
				{remainingCount > 0 && (
					<div className='flex items-center justify-center p-2 rounded-md bg-surface-secondary/30 border border-border-secondary border-dashed'>
						<Typography
							variant='body-sm'
							color='tertiary'>
							+{remainingCount} more member{remainingCount !== 1 ? 's' : ''}
						</Typography>
					</div>
				)}
			</div>
		</div>
	);
};

export default memo(MemberList);
