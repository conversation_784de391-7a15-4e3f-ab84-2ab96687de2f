import { clsx } from 'clsx';
import { Typography } from '../atoms';

export interface SidebarItemProps {
	icon: React.ReactNode;
	label: string;
	isActive?: boolean;
	isCollapsed?: boolean;
	onClick?: () => void;
	href?: string;
	badge?: React.ReactNode;
	disabled?: boolean;
}

const SidebarItem: React.FC<SidebarItemProps> = ({
	icon,
	label,
	isActive = false,
	isCollapsed = false,
	onClick,
	href,
	badge,
	disabled = false,
}) => {
	const baseClasses = clsx(
		'w-full flex items-center',
		'transition-all duration-200 ease-in-out',
		'rounded-lg',
		isCollapsed ? 'justify-center p-2' : 'justify-start px-3 py-2',
		isActive && [
			'bg-primary text-white',
			'shadow-md',
			'border border-primary-600',
			'ring-1 ring-primary-500/20',
		],
		!isActive && [
			'text-muted hover:text-foreground',
			'hover:bg-component-100',
			'border border-transparent',
			'hover:border-border-secondary',
			'hover:shadow-sm',
		],
		disabled && 'opacity-50 cursor-not-allowed',
		'focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-layout-100',
	);

	const content = (
		<>
			<span
				className={clsx(
					'flex-shrink-0 w-5 h-5',
					!isCollapsed && 'mr-3',
					isActive ? 'text-text-inverse' : 'text-current',
				)}>
				{icon}
			</span>
			{!isCollapsed && (
				<>
					<Typography
						variant='body-sm'
						weight='medium'
						color={isActive ? 'inverse' : 'primary'}
						className='truncate flex-1'>
						{label}
					</Typography>
					{badge && <span className='ml-2 flex-shrink-0'>{badge}</span>}
				</>
			)}
		</>
	);

	if (href) {
		return (
			<a
				href={href}
				className={baseClasses}
				onClick={onClick}
				aria-label={isCollapsed ? label : undefined}
				title={isCollapsed ? label : undefined}>
				{content}
			</a>
		);
	}

	return (
		<button
			type='button'
			className={baseClasses}
			onClick={onClick}
			disabled={disabled}
			aria-label={isCollapsed ? label : undefined}
			title={isCollapsed ? label : undefined}>
			{content}
		</button>
	);
};
SidebarItem.displayName = 'SidebarItem';
export default SidebarItem;
