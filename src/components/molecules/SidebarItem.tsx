import { clsx } from 'clsx';
import { Typography } from '../atoms';

export interface SidebarItemProps {
	icon: React.ReactNode;
	label: string;
	isActive?: boolean;
	isCollapsed?: boolean;
	onClick?: () => void;
	href?: string;
	badge?: React.ReactNode;
	disabled?: boolean;
}

const SidebarItem: React.FC<SidebarItemProps> = ({
	icon,
	label,
	isActive = false,
	isCollapsed = false,
	onClick,
	href,
	badge,
	disabled = false,
}) => {
	const baseClasses = clsx(
		'w-full flex items-center',
		'transition-all duration-200 ease-in-out',
		'rounded-md',
		isCollapsed ? 'justify-center p-1' : 'justify-start px-1 py-0.5',
		isActive && ['bg-primary text-white', 'shadow-sm', 'border border-primary'],
		!isActive && [
			'text-muted hover:text-foreground',
			'hover:bg-secondary/20',
			'border border-transparent',
			'hover:border-border-muted',
		],
		disabled && 'opacity-50 cursor-not-allowed',
		'focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-surface',
	);

	const content = (
		<>
			<span
				className={clsx(
					'flex-shrink-0 w-4 h-4',
					!isCollapsed && 'mr-1.5',
					isActive ? 'text-text-inverse' : 'text-current',
				)}>
				{icon}
			</span>
			{!isCollapsed && (
				<>
					<Typography
						variant='body-sm'
						weight='medium'
						color={isActive ? 'inverse' : 'primary'}
						className='truncate flex-1'>
						{label}
					</Typography>
					{badge && <span className='ml-1 flex-shrink-0'>{badge}</span>}
				</>
			)}
		</>
	);

	if (href) {
		return (
			<a
				href={href}
				className={baseClasses}
				onClick={onClick}
				aria-label={isCollapsed ? label : undefined}
				title={isCollapsed ? label : undefined}>
				{content}
			</a>
		);
	}

	return (
		<button
			type='button'
			className={baseClasses}
			onClick={onClick}
			disabled={disabled}
			aria-label={isCollapsed ? label : undefined}
			title={isCollapsed ? label : undefined}>
			{content}
		</button>
	);
};
SidebarItem.displayName = 'SidebarItem';
export default SidebarItem;
