import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Badge } from '@/components/atoms';
import { 
	ChevronDownIcon, 
	FolderIcon,
	PlusIcon,
	CheckIcon
} from '@heroicons/react/24/outline';
import { clsx } from 'clsx';

interface Project {
	id: string;
	name: string;
	description?: string;
	status: 'ACTIVE' | 'INACTIVE' | 'ARCHIVED';
	createdAt: string;
	updatedAt: string;
	createdBy: {
		id: string;
		email: string;
		fullName?: string;
	};
	members: Array<{
		user: {
			id: string;
			email: string;
			fullName?: string;
		};
	}>;
	_count: {
		generations: number;
		promptHistory: number;
	};
}

interface ProjectSelectorProps {
	selectedProject?: Project | null;
	onProjectSelect: (project: Project | null) => void;
	onCreateNew?: () => void;
	className?: string;
	placeholder?: string;
	required?: boolean;
	disabled?: boolean;
}

export const ProjectSelector: React.FC<ProjectSelectorProps> = ({
	selectedProject,
	onProjectSelect,
	onCreateNew,
	className,
	placeholder = "Select a project...",
	required = false,
	disabled = false,
}) => {
	const [isOpen, setIsOpen] = useState(false);
	const [projects, setProjects] = useState<Project[]>([]);
	const [isLoading, setIsLoading] = useState(true);

	// Mock data for now - will be replaced with real API call
	useEffect(() => {
		// Simulate API call to fetch user's projects
		setTimeout(() => {
			setProjects([
				{
					id: 'proj-1',
					name: 'Dashboard Project',
					description: 'Modern dashboard with analytics',
					status: 'ACTIVE',
					createdAt: new Date().toISOString(),
					updatedAt: new Date().toISOString(),
					createdBy: {
						id: 'user-1',
						email: '<EMAIL>',
						fullName: 'John Doe',
					},
					members: [],
					_count: {
						generations: 5,
						promptHistory: 12,
					},
				},
				{
					id: 'proj-2',
					name: 'E-commerce Platform',
					description: 'Online store with payment integration',
					status: 'ACTIVE',
					createdAt: new Date().toISOString(),
					updatedAt: new Date().toISOString(),
					createdBy: {
						id: 'user-1',
						email: '<EMAIL>',
						fullName: 'John Doe',
					},
					members: [],
					_count: {
						generations: 3,
						promptHistory: 8,
					},
				},
				{
					id: 'proj-3',
					name: 'Marketing Website',
					description: 'Company landing page and blog',
					status: 'ACTIVE',
					createdAt: new Date().toISOString(),
					updatedAt: new Date().toISOString(),
					createdBy: {
						id: 'user-1',
						email: '<EMAIL>',
						fullName: 'John Doe',
					},
					members: [],
					_count: {
						generations: 2,
						promptHistory: 4,
					},
				},
			]);
			setIsLoading(false);
		}, 500);
	}, []);

	const handleProjectSelect = (project: Project) => {
		onProjectSelect(project);
		setIsOpen(false);
	};

	const handleToggle = () => {
		if (!disabled) {
			setIsOpen(!isOpen);
		}
	};

	const getStatusBadge = (status: Project['status']) => {
		switch (status) {
			case 'ACTIVE':
				return { variant: 'success' as const, text: 'Active' };
			case 'INACTIVE':
				return { variant: 'secondary' as const, text: 'Inactive' };
			case 'ARCHIVED':
				return { variant: 'warning' as const, text: 'Archived' };
			default:
				return { variant: 'secondary' as const, text: 'Unknown' };
		}
	};

	return (
		<div className={clsx('relative', className)}>
			{/* Trigger Button */}
			<button
				type="button"
				onClick={handleToggle}
				disabled={disabled}
				className={clsx(
					'w-full flex items-center justify-between px-4 py-3 rounded-lg border transition-colors duration-200',
					'bg-surface-50 border-border-secondary',
					'hover:border-border focus:border-primary focus:ring-2 focus:ring-primary/20',
					disabled && 'opacity-50 cursor-not-allowed',
					isOpen && 'border-primary ring-2 ring-primary/20'
				)}>
				<div className='flex items-center gap-3'>
					{selectedProject ? (
						<>
							<FolderIcon className='w-5 h-5 text-primary' />
							<div className='text-left'>
								<Typography variant='body-sm' weight='medium'>
									{selectedProject.name}
								</Typography>
								{selectedProject.description && (
									<Typography variant='caption' color='tertiary' className='line-clamp-1'>
										{selectedProject.description}
									</Typography>
								)}
							</div>
						</>
					) : (
						<>
							<FolderIcon className='w-5 h-5 text-muted' />
							<Typography variant='body-sm' color='secondary'>
								{placeholder}
							</Typography>
						</>
					)}
				</div>
				<ChevronDownIcon 
					className={clsx(
						'w-5 h-5 text-muted transition-transform duration-200',
						isOpen && 'rotate-180'
					)} 
				/>
			</button>

			{/* Dropdown Menu */}
			{isOpen && (
				<div className='absolute top-full left-0 right-0 mt-2 bg-surface-50 border border-border-secondary rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto'>
					{isLoading ? (
						<div className='p-4 text-center'>
							<div className='animate-spin rounded-full h-6 w-6 border-2 border-border-secondary border-t-primary mx-auto mb-2'></div>
							<Typography variant='caption' color='secondary'>
								Loading projects...
							</Typography>
						</div>
					) : (
						<>
							{/* Project List */}
							{projects.length > 0 ? (
								<div className='py-2'>
									{projects.map((project) => {
										const statusBadge = getStatusBadge(project.status);
										const isSelected = selectedProject?.id === project.id;
										
										return (
											<button
												key={project.id}
												type="button"
												onClick={() => handleProjectSelect(project)}
												className={clsx(
													'w-full flex items-center justify-between px-4 py-3 hover:bg-surface-100 transition-colors duration-200',
													isSelected && 'bg-primary/5'
												)}>
												<div className='flex items-center gap-3 flex-1 min-w-0'>
													<FolderIcon className='w-5 h-5 text-primary flex-shrink-0' />
													<div className='text-left flex-1 min-w-0'>
														<div className='flex items-center gap-2 mb-1'>
															<Typography variant='body-sm' weight='medium' className='truncate'>
																{project.name}
															</Typography>
															<Badge
																variant={statusBadge.variant}
																size='small'
																emphasis='light'
																className='flex-shrink-0'>
																{statusBadge.text}
															</Badge>
														</div>
														{project.description && (
															<Typography variant='caption' color='tertiary' className='line-clamp-1'>
																{project.description}
															</Typography>
														)}
														<div className='flex items-center gap-3 mt-1'>
															<Typography variant='caption' color='secondary'>
																{project._count.generations} generations
															</Typography>
															<Typography variant='caption' color='secondary'>
																{project._count.promptHistory} prompts
															</Typography>
														</div>
													</div>
												</div>
												{isSelected && (
													<CheckIcon className='w-5 h-5 text-primary flex-shrink-0' />
												)}
											</button>
										);
									})}
								</div>
							) : (
								<div className='p-4 text-center'>
									<Typography variant='body-sm' color='secondary'>
										No projects found
									</Typography>
								</div>
							)}

							{/* Create New Project Option */}
							{onCreateNew && (
								<>
									<div className='border-t border-border-secondary'></div>
									<div className='p-2'>
										<button
											type="button"
											onClick={() => {
												onCreateNew();
												setIsOpen(false);
											}}
											className='w-full flex items-center gap-3 px-4 py-3 hover:bg-surface-100 transition-colors duration-200 rounded-lg'>
											<PlusIcon className='w-5 h-5 text-primary' />
											<Typography variant='body-sm' weight='medium' color='primary'>
												Create New Project
											</Typography>
										</button>
									</div>
								</>
							)}
						</>
					)}
				</div>
			)}

			{/* Click Outside Handler */}
			{isOpen && (
				<div 
					className='fixed inset-0 z-40' 
					onClick={() => setIsOpen(false)}
				/>
			)}
		</div>
	);
};
