import { useState, useRef, forwardRef } from 'react';
import { clsx } from 'clsx';
import { PaperAirplaneIcon } from '@heroicons/react/24/outline';
import { Textarea, Button, Typography } from '@/components/atoms';

interface PromptInputProps {
	value?: string;
	onChange?: (value: string) => void;
	onSubmit?: (prompt: string) => void;
	placeholder?: string;
	disabled?: boolean;
	loading?: boolean;
	maxLength?: number;
	minLength?: number;
	showCharacterCount?: boolean;
	submitButtonText?: string;
	className?: string;
	textareaClassName?: string;
	buttonClassName?: string;
	size?: 'small' | 'medium' | 'large';
	variant?: 'default' | 'compact';
	autoFocus?: boolean;
	rows?: number;
	onKeyDown?: (event: React.KeyboardEvent<HTMLTextAreaElement>) => void;
}

const PromptInput = forwardRef<HTMLTextAreaElement, PromptInputProps>(
	(
		{
			value = '',
			onChange,
			onSubmit,
			placeholder = 'Enter your prompt here...',
			disabled = false,
			loading = false,
			maxLength = 2000,
			minLength = 1,
			showCharacterCount = true,
			submitButtonText = 'Generate',
			className,
			textareaClassName,
			buttonClassName,
			size = 'medium',
			variant = 'default',
			autoFocus = false,
			rows,
			onKeyDown,
		},
		ref,
	) => {
		const [internalValue, setInternalValue] = useState(value);
		const textareaRef = useRef<HTMLTextAreaElement>(null);

		// Use forwarded ref or internal ref
		const resolvedRef = ref || textareaRef;

		const currentValue = onChange ? value : internalValue;
		const characterCount = currentValue.length;
		const isOverLimit = maxLength && characterCount > maxLength;
		const isUnderMinimum = minLength && characterCount < minLength;
		const canSubmit =
			!disabled &&
			!loading &&
			!isOverLimit &&
			!isUnderMinimum &&
			currentValue.trim().length > 0;

		const handleChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
			const newValue = event.target.value;

			if (onChange) {
				onChange(newValue);
			} else {
				setInternalValue(newValue);
			}
		};

		const handleSubmit = () => {
			if (canSubmit && onSubmit) {
				onSubmit(currentValue.trim());
			}
		};

		const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
			// Handle Ctrl/Cmd + Enter for submit
			if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
				event.preventDefault();
				handleSubmit();
			}

			// Call custom onKeyDown if provided
			onKeyDown?.(event);
		};

		const sizeConfig = {
			small: {
				textareaSize: 'small' as const,
				buttonSize: 'sm' as const,
				minHeight: variant === 'compact' ? 'min-h-[3rem]' : 'min-h-[4rem]',
				buttonSpacing: 'bottom-2 right-2',
				paddingRight: 'pr-12',
			},
			medium: {
				textareaSize: 'medium' as const,
				buttonSize: 'md' as const,
				minHeight: variant === 'compact' ? 'min-h-[4rem]' : 'min-h-[5rem]',
				buttonSpacing: 'bottom-2 right-2',
				paddingRight: 'pr-14',
			},
			large: {
				textareaSize: 'large' as const,
				buttonSize: 'lg' as const,
				minHeight: variant === 'compact' ? 'min-h-[5rem]' : 'min-h-[6rem]',
				buttonSpacing: 'bottom-2 right-2',
				paddingRight: 'pr-16',
			},
		};

		const config = sizeConfig[size];

		return (
			<div className={clsx('relative', className)}>
				{/* Main input container */}
				<div className='relative'>
					<Textarea
						ref={resolvedRef}
						value={currentValue}
						onChange={handleChange}
						onKeyDown={handleKeyDown}
						placeholder={placeholder}
						disabled={disabled}
						textareaSize={config.textareaSize}
						variant={isOverLimit ? 'error' : 'default'}
						rows={rows}
						autoFocus={autoFocus}
						className={clsx(
							config.minHeight,
							config.paddingRight, // Space for submit button
							variant === 'compact' && 'resize-none',
							textareaClassName,
							'shadow-sm',
						)}
					/>

					{/* Submit button positioned inside textarea */}
					<div className={clsx('absolute', config.buttonSpacing)}>
						<Button
							onClick={handleSubmit}
							disabled={!canSubmit}
							variant='primary'
							size={config.buttonSize}
							className={clsx('shadow-sm', buttonClassName)}
							aria-label={submitButtonText}>
							{loading ? (
								<div className='w-4 h-4 animate-spin rounded-full border-2 border-white border-t-transparent' />
							) : (
								<PaperAirplaneIcon className='w-4 h-4' />
							)}
							{variant !== 'compact' && (
								<span className='ml-1 hidden sm:inline'>
									{submitButtonText}
								</span>
							)}
						</Button>
					</div>
				</div>

				{/* Footer with character count and hints */}
				{(showCharacterCount || variant !== 'compact') && (
					<div className='flex items-center justify-between mt-2 px-1'>
						<div className='flex items-center gap-2 text-xs text-muted'>
							{variant !== 'compact' && (
								<Typography
									variant='caption'
									color='tertiary'>
									Press Ctrl+Enter to submit
								</Typography>
							)}
						</div>

						{showCharacterCount && (
							<Typography
								variant='caption'
								color={isOverLimit ? 'negative' : 'tertiary'}
								className={clsx('tabular-nums', isOverLimit && 'font-medium')}>
								{characterCount}
								{maxLength && `/${maxLength}`}
							</Typography>
						)}
					</div>
				)}
			</div>
		);
	},
);

PromptInput.displayName = 'PromptInput';

export default PromptInput;
export type { PromptInputProps };
