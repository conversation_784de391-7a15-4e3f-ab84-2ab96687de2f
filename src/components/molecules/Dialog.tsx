import {
	Dialog as HeadlessDialog,
	DialogPanel,
	DialogTitle,
	DialogBackdrop,
} from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { clsx } from 'clsx';
import { Button, Typography } from '@/components/atoms';

interface DialogProps {
	isOpen: boolean;
	onClose: () => void;
	title?: string;
	description?: string;
	children: React.ReactNode;
	size?: 'sm' | 'md' | 'lg' | 'xl';
	showCloseButton?: boolean;
	className?: string;
}

const dialogSizes = {
	sm: 'max-w-sm m-2 sm:mx-auto w-full sm:w-auto',
	md: 'max-w-md m-2 sm:mx-auto w-full sm:w-auto',
	lg: 'max-w-lg m-2 sm:mx-auto w-full sm:w-auto',
	xl: 'max-w-xl m-2 sm:mx-auto w-full sm:w-auto',
};

const Dialog: React.FC<DialogProps> = ({
	isOpen,
	onClose,
	title,
	description,
	children,
	size = 'md',
	showCloseButton = true,
	className,
}) => {
	return (
		<HeadlessDialog
			open={isOpen}
			onClose={onClose}
			className='relative z-50'>
			{/* Backdrop */}
			<DialogBackdrop
				className={clsx(
					'fixed inset-0 bg-black/20 dark:bg-black/50 backdrop-blur-sm',
					'transition-opacity duration-200 ease-in-out',
					'data-[closed]:opacity-0',
				)}
			/>

			{/* Dialog container */}
			<div className='fixed inset-0 flex items-end sm:items-center justify-center p-0 sm:p-2'>
				<DialogPanel
					className={clsx(
						'w-full rounded-md sm:rounded-md bg-surface shadow-lg',
						'border-t border-l border-r sm:border border-border-secondary',
						'transition-all duration-200 ease-in-out',
						'max-h-[90vh] sm:max-h-[80vh] overflow-hidden',
						'data-[closed]:translate-y-full sm:data-[closed]:scale-95 data-[closed]:opacity-0',
						'data-[enter]:duration-200 data-[enter]:ease-out',
						'data-[leave]:duration-150 data-[leave]:ease-in',
						'ring-1 ring-border-muted/20',
						dialogSizes[size],
						className,
					)}>
					{/* Header */}
					{(title || showCloseButton) && (
						<div className='flex items-start justify-between p-4 border-b border-border-secondary'>
							<div className='flex-1'>
								{title && (
									<DialogTitle>
										<Typography
											variant='h3'
											as='span'
											className='mb-1'>
											{title}
										</Typography>
									</DialogTitle>
								)}
								{description && (
									<Typography
										variant='body'
										color='secondary'>
										{description}
									</Typography>
								)}
							</div>
							{showCloseButton && (
								<Button
									variant='link'
									size='sm'
									onClick={onClose}
									className='mt-2 mr-1'
									aria-label='Close dialog'>
									<XMarkIcon className='w-4 h-4' />
								</Button>
							)}
						</div>
					)}

					{/* Content */}
					<div className='p-3 sm:p-4 overflow-y-auto flex-1'>{children}</div>
				</DialogPanel>
			</div>
		</HeadlessDialog>
	);
};

export default Dialog;
