import { useState, useCallback } from 'react';

export interface Generation {
	id: string;
	prompt: string;
	preview?: string;
	timestamp: string;
	status: 'pending' | 'completed' | 'failed';
	views: number;
	createdAt: Date;
}

interface UseGenerationsReturn {
	generations: Generation[];
	isGenerating: boolean;
	error: string | null;
	generateUI: (prompt: string) => Promise<void>;
	clearError: () => void;
	getRecentGenerations: (limit?: number) => Generation[];
}

// Mock data for development
const mockGenerations: Generation[] = [
	{
		id: '1',
		prompt: 'Create a modern dashboard with dark theme and analytics charts',
		preview: '/api/placeholder/400/300',
		timestamp: '2 hours ago',
		status: 'completed',
		views: 12,
		createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
	},
	{
		id: '2',
		prompt: 'Design a landing page for a SaaS product with hero section',
		preview: '/api/placeholder/400/300',
		timestamp: '5 hours ago',
		status: 'completed',
		views: 8,
		createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000),
	},
	{
		id: '3',
		prompt: 'Build a responsive e-commerce product card component',
		preview: '/api/placeholder/400/300',
		timestamp: '1 day ago',
		status: 'completed',
		views: 24,
		createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
	},
	{
		id: '4',
		prompt: 'Create a modern login form with social authentication',
		preview: '/api/placeholder/400/300',
		timestamp: '2 days ago',
		status: 'completed',
		views: 15,
		createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
	},
];

export const useGenerations = (): UseGenerationsReturn => {
	const [generations, setGenerations] = useState<Generation[]>(mockGenerations);
	const [isGenerating, setIsGenerating] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const generateUI = useCallback(async (prompt: string) => {
		if (!prompt.trim()) {
			setError('Please enter a prompt');
			return;
		}

		setIsGenerating(true);
		setError(null);

		try {
			// Simulate API call delay
			await new Promise(resolve => setTimeout(resolve, 3000));

			// Create new generation
			const newGeneration: Generation = {
				id: Date.now().toString(),
				prompt: prompt.trim(),
				timestamp: 'Just now',
				status: 'completed',
				views: 0,
				createdAt: new Date(),
			};

			// Add to the beginning of the list
			setGenerations(prev => [newGeneration, ...prev]);

		} catch (err) {
			setError(err instanceof Error ? err.message : 'Failed to generate UI');
		} finally {
			setIsGenerating(false);
		}
	}, []);

	const clearError = useCallback(() => {
		setError(null);
	}, []);

	const getRecentGenerations = useCallback((limit = 6) => {
		return generations
			.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
			.slice(0, limit);
	}, [generations]);

	return {
		generations,
		isGenerating,
		error,
		generateUI,
		clearError,
		getRecentGenerations,
	};
};
