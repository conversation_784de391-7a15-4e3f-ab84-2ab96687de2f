import { useEffect, useState } from 'react';
import { useUsers } from './useUsers';
import type { User } from '@/api/UserApi';
import type { SelectOption } from '@/components/atoms/Select';

interface UseMemberSelectionReturn {
	users: User[];
	loading: boolean;
	userOptions: SelectOption[];
	selectedOptions: SelectOption[];
	initializeUsers: () => void;
}

export const useMemberSelection = (selectedMembers: User[]): UseMemberSelectionReturn => {
	const { users, loading, getAllUsers } = useUsers();
	const [hasInitialized, setHasInitialized] = useState(false);

	const initializeUsers = () => {
		if (!hasInitialized) {
			getAllUsers({ status: 'ACTIVE', limit: 50 });
			setHasInitialized(true);
		}
	};

	useEffect(() => {
		initializeUsers();
	}, []);

	const userOptions: SelectOption[] = users.map((user) => ({
		id: user.id,
		label: user.fullName || user.email || 'Unknown User',
		value: user.id,
		description: user.email || '',
		avatar: user.avatar,
		disabled: user.isActive === false,
	}));

	const selectedOptions: SelectOption[] = selectedMembers.map((user) => ({
		id: user.id,
		label: user.fullName || user.email || 'Unknown User',
		value: user.id,
		description: user.email || '',
		avatar: user.avatar,
		disabled: user.isActive === false,
	}));

	return {
		users,
		loading,
		userOptions,
		selectedOptions,
		initializeUsers,
	};
};
