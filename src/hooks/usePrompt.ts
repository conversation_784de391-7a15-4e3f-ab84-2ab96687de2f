import { useState, useCallback } from 'react';
import { savePromptClientSide, SavePromptResult } from '@/services/promptService';
import { SavePromptDto } from '@/api/PromptApi';
import { useAuthStore } from '@/providers/auth-store-provider';

interface UsePromptReturn {
	isLoading: boolean;
	error: string | null;
	savePrompt: (promptData: Omit<SavePromptDto, 'createdById'>) => Promise<SavePromptResult | null>;
	clearError: () => void;
}

export const usePrompt = (): UsePromptReturn => {
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const { user } = useAuthStore((state) => state);

	const savePrompt = useCallback(
		async (promptData: Omit<SavePromptDto, 'createdById'>): Promise<SavePromptResult | null> => {
			if (!user?.id) {
				setError('User not authenticated');
				return null;
			}

			if (!promptData.prompt.trim()) {
				setError('Please enter a prompt');
				return null;
			}

			setIsLoading(true);
			setError(null);

			try {
				const result = await savePromptClientSide({
					...promptData,
					createdById: user.id,
				});

				if (result.error) {
					setError(result.error);
					return null;
				}

				return result.prompt || null;
			} catch (err) {
				const errorMessage = err instanceof Error ? err.message : 'Failed to save prompt';
				setError(errorMessage);
				return null;
			} finally {
				setIsLoading(false);
			}
		},
		[user?.id]
	);

	const clearError = useCallback(() => {
		setError(null);
	}, []);

	return {
		isLoading,
		error,
		savePrompt,
		clearError,
	};
};
