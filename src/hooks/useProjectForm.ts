import { useState, useEffect } from 'react';
import { useProjects } from './useProjects';
import type { User } from '@/api/UserApi';
import type { Project } from '@/components/organisms/ProjectsGrid';
import { getProjectMemberUsers } from '@/components/organisms/ProjectsGrid';

interface ProjectFormData {
	name: string;
	description: string;
	members?: User[];
}

interface UseProjectFormProps {
	mode: 'create' | 'edit';
	project?: Project | null;
	isOpen: boolean;
	onSuccess?: () => void;
	onProjectCreated?: (
		projectData: Omit<ProjectFormData, 'members'>,
	) => Promise<Project | null>;
	onProjectUpdated?: (
		projectId: string,
		projectData: Omit<ProjectFormData, 'members'>,
	) => Promise<Project | null>;
}

interface UseProjectFormReturn {
	isSubmitting: boolean;
	error: string | null;
	selectedMembers: User[];
	setSelectedMembers: (members: User[]) => void;
	handleSubmit: (event: React.FormEvent<HTMLFormElement>) => Promise<void>;
	clearError: () => void;
}

export const useProjectForm = ({
	mode,
	project,
	isOpen,
	onSuccess,
	onProjectCreated,
	onProjectUpdated,
}: UseProjectFormProps): UseProjectFormReturn => {
	const {
		createProject,
		updateProject,
		addProjectMembers,
		removeProjectMembers,
	} = useProjects();
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [selectedMembers, setSelectedMembers] = useState<User[]>([]);

	useEffect(() => {
		if (isOpen) {
			setError(null);
			if (mode === 'edit' && project?.members) {
				setSelectedMembers(getProjectMemberUsers(project.members));
			} else {
				setSelectedMembers([]);
			}
		}
	}, [isOpen, project, mode]);

	const handleMemberChanges = async (
		projectId: string,
		newMembers: User[],
		originalMembers: User[] = [],
	) => {
		const newMemberIds = newMembers.map((m) => m.id);
		const originalMemberIds = originalMembers.map((m) => m.id);

		const membersToAdd = newMemberIds.filter(
			(id) => !originalMemberIds.includes(id),
		);
		const membersToRemove = originalMemberIds.filter(
			(id) => !newMemberIds.includes(id),
		);

		if (membersToAdd.length > 0) {
			const addResult = await addProjectMembers(projectId, membersToAdd);
			if (!addResult) {
				throw new Error('Failed to add members');
			}
		}

		if (membersToRemove.length > 0) {
			const removeResult = await removeProjectMembers(
				projectId,
				membersToRemove,
			);
			if (!removeResult) {
				throw new Error('Failed to remove members');
			}
		}
	};

	const handleCreateProject = async (
		projectData: ProjectFormData,
		form: HTMLFormElement,
	) => {
		const basicProjectData = {
			name: projectData.name,
			description: projectData.description,
		};

		let createdProject;
		if (onProjectCreated) {
			createdProject = await onProjectCreated(basicProjectData);
		} else {
			createdProject = await createProject(basicProjectData);
		}

		if (!createdProject) {
			throw new Error('Failed to create project');
		}

		if (selectedMembers.length > 0) {
			const memberIds = selectedMembers.map((m) => m.id);
			await addProjectMembers(createdProject.id, memberIds);
		}

		form.reset();
		onSuccess?.();
	};

	const handleUpdateProject = async (projectData: ProjectFormData) => {
		if (!project) {
			throw new Error('No project selected for editing');
		}

		const basicProjectData = {
			name: projectData.name,
			description: projectData.description,
		};

		let updatedProject;
		if (onProjectUpdated) {
			updatedProject = await onProjectUpdated(project.id, basicProjectData);
		} else {
			updatedProject = await updateProject(project.id, basicProjectData);
		}

		if (!updatedProject) {
			throw new Error('Failed to update project');
		}

		await handleMemberChanges(
			project.id,
			selectedMembers,
			getProjectMemberUsers(project.members),
		);

		onSuccess?.();
	};

	const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
		event.preventDefault();
		setIsSubmitting(true);
		setError(null);

		const formData = new FormData(event.currentTarget);
		const projectData: ProjectFormData = {
			name: (formData.get('name') as string).trim(),
			description: (formData.get('description') as string).trim(),
			members: selectedMembers,
		};

		try {
			if (mode === 'create') {
				await handleCreateProject(projectData, event.currentTarget);
			} else {
				await handleUpdateProject(projectData);
			}
		} catch (error) {
			setError(
				error instanceof Error ? error.message : 'An unexpected error occurred',
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	const clearError = () => {
		setError(null);
	};

	return {
		isSubmitting,
		error,
		selectedMembers,
		setSelectedMembers,
		handleSubmit,
		clearError,
	};
};
