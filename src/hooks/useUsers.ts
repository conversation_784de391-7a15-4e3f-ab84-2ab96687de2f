import { useState } from 'react';
import {
	UserAPI,
	type User,
	type GetUsersQueryParams,
	type CreateUserDto,
	type UpdateUserDto,
	type UpdateUserRoleDto,
	type UserStatsResponse,
} from '@/api/UserApi';
import {
	extractApiData,
	getApiErrorMessage,
	isApiSuccess,
} from '@/common/utils/apiResponse';

interface UseUsersReturn {
	users: User[];
	loading: boolean;
	error: string | null;
	totalUsers: number;
	getAllUsers: (params?: GetUsersQueryParams) => Promise<User[]>;
	getUserById: (id: string) => Promise<User | null>;
	getUserStats: (id: string) => Promise<UserStatsResponse | null>;
	createUser: (userData: CreateUserDto) => Promise<User | null>;
	updateUser: (id: string, userData: UpdateUserDto) => Promise<User | null>;
	updateUserRole: (
		id: string,
		roleData: UpdateUserRoleDto,
	) => Promise<User | null>;
	toggleUserStatus: (id: string) => Promise<User | null>;
	deleteUser: (id: string) => Promise<boolean>;
	getCurrentUser: () => Promise<User | null>;
}

export const useUsers = (): UseUsersReturn => {
	const [users, setUsers] = useState<User[]>([]);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [totalUsers, setTotalUsers] = useState(0);

	// Create API instance once
	const userAPI = new UserAPI();

	const getAllUsers = async (params: GetUsersQueryParams = {}) => {
		setLoading(true);
		setError(null);

		try {
			const response = await userAPI.getAllUsers(params);

			if (isApiSuccess(response)) {
				const data = extractApiData(response);
				let userList: User[] = [];
				let total = 0;

				if (Array.isArray(data)) {
					userList = data;
					total = data.length;
				} else if (data && typeof data === 'object') {
					const responseData = data as Record<string, unknown>;
					if (Array.isArray(responseData.users)) {
						userList = responseData.users as User[];
						total =
							(responseData.total as number) ||
							(responseData.count as number) ||
							userList.length;
					} else if (Array.isArray(responseData.data)) {
						userList = responseData.data as User[];
						total =
							(responseData.total as number) ||
							(responseData.count as number) ||
							userList.length;
					} else {
						userList = [data as User];
						total = 1;
					}
				}

				setUsers(userList);
				setTotalUsers(total);
				return userList;
			} else {
				const errorMessage =
					getApiErrorMessage(response) || 'Failed to fetch users';
				setError(errorMessage);
				setUsers([]);
				setTotalUsers(0);
				return [];
			}
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : 'Network error occurred';
			setError(errorMessage);
			setUsers([]);
			setTotalUsers(0);
			return [];
		} finally {
			setLoading(false);
		}
	};

	const getUserById = async (id: string) => {
		setError(null);

		try {
			const response = await userAPI.getUserById(id);

			if (isApiSuccess(response)) {
				return extractApiData(response) as User;
			} else {
				const errorMessage =
					getApiErrorMessage(response) || 'Failed to get user';
				setError(errorMessage);
				return null;
			}
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : 'Network error occurred';
			setError(errorMessage);
			return null;
		}
	};

	const getUserStats = async (id: string) => {
		setError(null);

		try {
			const response = await userAPI.getUserStats(id);

			if (isApiSuccess(response)) {
				return extractApiData(response) as UserStatsResponse;
			} else {
				const errorMessage =
					getApiErrorMessage(response) || 'Failed to get user stats';
				setError(errorMessage);
				return null;
			}
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : 'Network error occurred';
			setError(errorMessage);
			return null;
		}
	};

	const createUser = async (userData: CreateUserDto) => {
		setLoading(true);
		setError(null);

		try {
			const response = await userAPI.createUser(userData);

			if (isApiSuccess(response)) {
				const newUser = extractApiData(response) as User;
				setUsers((prev) => [...prev, newUser]);
				setTotalUsers((prev) => prev + 1);
				return newUser;
			} else {
				const errorMessage =
					getApiErrorMessage(response) || 'Failed to create user';
				setError(errorMessage);
				return null;
			}
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : 'Network error occurred';
			setError(errorMessage);
			return null;
		} finally {
			setLoading(false);
		}
	};

	const updateUser = async (id: string, userData: UpdateUserDto) => {
		setLoading(true);
		setError(null);

		try {
			const response = await userAPI.updateUser(id, userData);

			if (isApiSuccess(response)) {
				const updatedUser = extractApiData(response) as User;
				setUsers((prev) =>
					prev.map((user) => (user.id === id ? updatedUser : user)),
				);
				return updatedUser;
			} else {
				const errorMessage =
					getApiErrorMessage(response) || 'Failed to update user';
				setError(errorMessage);
				return null;
			}
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : 'Network error occurred';
			setError(errorMessage);
			return null;
		} finally {
			setLoading(false);
		}
	};

	const updateUserRole = async (id: string, roleData: UpdateUserRoleDto) => {
		setLoading(true);
		setError(null);

		try {
			const response = await userAPI.updateUserRole(id, roleData);

			if (isApiSuccess(response)) {
				const updatedUser = extractApiData(response) as User;
				setUsers((prev) =>
					prev.map((user) => (user.id === id ? updatedUser : user)),
				);
				return updatedUser;
			} else {
				const errorMessage =
					getApiErrorMessage(response) || 'Failed to update user role';
				setError(errorMessage);
				return null;
			}
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : 'Network error occurred';
			setError(errorMessage);
			return null;
		} finally {
			setLoading(false);
		}
	};

	const toggleUserStatus = async (id: string) => {
		setLoading(true);
		setError(null);

		try {
			const response = await userAPI.toggleUserStatus(id);

			if (isApiSuccess(response)) {
				const updatedUser = extractApiData(response) as User;
				setUsers((prev) =>
					prev.map((user) => (user.id === id ? updatedUser : user)),
				);
				return updatedUser;
			} else {
				const errorMessage =
					getApiErrorMessage(response) || 'Failed to toggle user status';
				setError(errorMessage);
				return null;
			}
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : 'Network error occurred';
			setError(errorMessage);
			return null;
		} finally {
			setLoading(false);
		}
	};

	const deleteUser = async (id: string) => {
		setLoading(true);
		setError(null);

		try {
			const response = await userAPI.deleteUser(id);

			if (isApiSuccess(response)) {
				setUsers((prev) => prev.filter((user) => user.id !== id));
				setTotalUsers((prev) => prev - 1);
				return true;
			} else {
				const errorMessage =
					getApiErrorMessage(response) || 'Failed to delete user';
				setError(errorMessage);
				return false;
			}
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : 'Network error occurred';
			setError(errorMessage);
			return false;
		} finally {
			setLoading(false);
		}
	};

	const getCurrentUser = async () => {
		setError(null);

		try {
			const response = await userAPI.getCurrentUser();

			if (isApiSuccess(response)) {
				return extractApiData(response) as User;
			} else {
				const errorMessage =
					getApiErrorMessage(response) || 'Failed to get current user';
				setError(errorMessage);
				return null;
			}
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : 'Network error occurred';
			setError(errorMessage);
			return null;
		}
	};

	return {
		users,
		loading,
		error,
		totalUsers,
		getAllUsers,
		getUserById,
		getUserStats,
		createUser,
		updateUser,
		updateUserRole,
		toggleUserStatus,
		deleteUser,
		getCurrentUser,
	};
};
