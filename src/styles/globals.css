@import "tailwindcss";


:root {

  /* Piximind colors */
  --primary-50: oklch(0.9628 0.0458 120.59);
  --primary-100: oklch(0.9256 0.0912 122.74);
  --primary-200: oklch(0.8908 0.1336 123.76);
  --primary-300: oklch(0.8579 0.1699 125.01);
  --primary-400: oklch(0.7859 0.1885 128.62);
  --primary-500: oklch(0.7396 0.203872 131.5587);
  --primary-600: oklch(0.604 0.165559 131.1971);
  --primary-700: oklch(0.5589 0.1516 131.45);
  --primary-800: oklch(0.4743 0.1234 130.6);
  --primary-900: oklch(0.4201 0.105 130.98);

  --gray-50: oklch(0.9452 0.0058 153.77);
  --gray-100: oklch(0.9025 0.0069 145.52);
  --gray-200: oklch(0.8536 0.0112 149.84);
  --gray-300: oklch(0.8165 0.0156 151.72);
  --gray-400: oklch(0.6792 0.0272 148.91);
  --gray-500: oklch(0.5945 0.0364 149.19);
  --gray-600: oklch(0.525 0.0394 148.8);
  --gray-700: oklch(0.4454 0.0298 152.16);
  --gray-800: oklch(0.3454 0.017 152.01);
  --gray-900: oklch(0.3166 0.013 154.51);

  --blue-50: oklch(0.9429 0.0303 194.11);
  --blue-100: oklch(0.9029 0.0447 191.16);
  --blue-200: oklch(0.856 0.0605 189.54);
  --blue-300: oklch(0.8115 0.0738 192.53);
  --blue-400: oklch(0.6754 0.1033 190.09);
  --blue-500: oklch(0.6069 0.105169 188.9711);
  --blue-600: oklch(0.5417 0.0926 189.95);
  --blue-700: oklch(0.5168 0.0889 189.68);
  --blue-800: oklch(0.4593 0.0762 192.39);
  --blue-900: oklch(0.4051 0.0651 191.94);

  --pink-50: oklch(0.9305 0.0349 352.87);
  --pink-100: oklch(0.9004 0.0576 356.36);
  --pink-200: oklch(0.8306 0.0845 356.17);
  --pink-300: oklch(0.781 0.121 358.19);
  --pink-400: oklch(0.6489 0.1954 3.57);
  --pink-500: oklch(0.5767 0.2305 14.37);
  --pink-600: oklch(0.5184 0.2068 13.88);
  --pink-700: oklch(0.4725 0.1883 13.07);
  --pink-800: oklch(0.417 0.166 12.19);
  --pink-900: oklch(0.2831 0.1133 8.67);

  /* System colors */

  --text-50: oklch(0.9816 0.0017 247.84);
  --text-100: oklch(0.9629 0.0025 228.78);
  --text-200: oklch(0.9116 0.0063 255.48);
  --text-300: oklch(0.8493 0.01 252.82);
  --text-400: oklch(0.6755 0.0211 252.95);
  --text-500: oklch(0.4932 0.035 251.56);
  --text-600: oklch(0.428 0.0403 251.42);
  --text-700: oklch(0.391 0.0432 251.43);
  --text-800: oklch(0.3501 0.0479 252.59);
  --text-900: oklch(0.3117 0.0512 252.71);

  --success-50: oklch(0.9727 0.024 151.19);
  --success-100: oklch(0.9463 0.0591 148.83);
  --success-200: oklch(0.9038 0.0987 148.43);
  --success-300: oklch(0.8562 0.141 147.8);
  --success-400: oklch(0.802 0.1684 147.32);
  --success-500: oklch(0.7602 0.1832 146.61);
  --success-600: oklch(0.7146 0.1835 146.44);
  --success-700: oklch(0.6741 0.1775 146.24);
  --success-800: oklch(0.6172 0.1624 146.43);
  --success-900: oklch(0.5604 0.1427 146.89);

  --warning-50: oklch(0.979 0.0396 97.76);
  --warning-100: oklch(0.961 0.0679 96.59);
  --warning-200: oklch(0.9402 0.1049 96.83);
  --warning-300: oklch(0.9097 0.144 95.11);
  --warning-400: oklch(0.8826 0.1648 92.22);
  --warning-500: oklch(0.8471 0.1693 87.12);
  --warning-600: oklch(0.8069 0.1674 78.12);
  --warning-700: oklch(0.7702 0.1658 71.02);
  --warning-800: oklch(0.7311 0.1673 62.62);
  --warning-900: oklch(0.6835 0.1693 54.96);

  --danger-50: oklch(0.9779 0.0107 17.34);
  --danger-100: oklch(0.9387 0.030868 17.7147);
  --danger-200: oklch(0.8834 0.0616 18.39);
  --danger-300: oklch(0.8169 0.103153 19.5306);
  --danger-400: oklch(0.7563 0.146 21.1);
  --danger-500: oklch(0.7116 0.1812 22.84);
  --danger-600: oklch(0.6706 0.2045 24.52);
  --danger-700: oklch(0.6324 0.2144 25.84);
  --danger-800: oklch(0.5928 0.2106 26.53);
  --danger-900: oklch(0.5457 0.1949 26.61);


  /* Spacing */
  --spacing-scale-xs: 10px;
  --spacing-scale-sm: 12px;
  --spacing-scale-md: 16px;
  --spacing-scale-lg: 20px;
  --spacing-scale-xl: 24px;

  /* Typography */

  /* Body Text */
  --text-scale-xs: 12px;
  --text-scale-sm: 14px;
  --text-scale-md: 16px;
  --text-scale-lg: 18px;
  --text-scale-xl: 20px;

  /* Headings */
  --text-scale-h1: 34px;
  --text-scale-h2: 26px;
  --text-scale-h3: 22px;
  --text-scale-h4: 18px;
  --text-scale-h5: 16px;
  --text-scale-h6: 14px;

  --background: var(--color-neutral-100);
  --foreground: var(--color-neutral-800);
}

/* ===== DARK MODE ===== */
.dark {
  /* Piximind colors - adjusted for dark mode readability with more vibrant primary */
  --primary-50: oklch(0.15 0.05 130.98);
  --primary-100: oklch(0.22 0.07 130.6);
  --primary-200: oklch(0.3 0.09 131.45);
  --primary-300: oklch(0.38 0.11 131.1971);
  --primary-400: oklch(0.48 0.13 131.5587);
  --primary-500: oklch(0.58 0.15 128.62);
  --primary-600: oklch(0.68 0.14 125.01);
  --primary-700: oklch(0.76 0.12 123.76);
  --primary-800: oklch(0.83 0.09 122.74);
  --primary-900: oklch(0.9 0.05 120.59);

  /* Enhanced gray scale for better contrast */
  --gray-50: oklch(0.15 0.008 154.51);
  --gray-100: oklch(0.18 0.01 152.01);
  --gray-200: oklch(0.22 0.012 152.16);
  --gray-300: oklch(0.28 0.015 148.8);
  --gray-400: oklch(0.38 0.018 149.19);
  --gray-500: oklch(0.5 0.02 148.91);
  --gray-600: oklch(0.65 0.015 151.72);
  --gray-700: oklch(0.75 0.01 149.84);
  --gray-800: oklch(0.85 0.008 145.52);
  --gray-900: oklch(0.92 0.005 153.77);

  /* Blue colors with enhanced contrast */
  --blue-50: oklch(0.25 0.04 191.94);
  --blue-100: oklch(0.32 0.05 192.39);
  --blue-200: oklch(0.4 0.06 189.68);
  --blue-300: oklch(0.5 0.07 189.95);
  --blue-400: oklch(0.6 0.08 188.9711);
  --blue-500: oklch(0.7 0.085 190.09);
  --blue-600: oklch(0.78 0.075 192.53);
  --blue-700: oklch(0.84 0.06 189.54);
  --blue-800: oklch(0.89 0.045 191.16);
  --blue-900: oklch(0.93 0.03 194.11);

  /* Pink colors with better dark mode visibility */
  --pink-50: oklch(0.22 0.06 8.67);
  --pink-100: oklch(0.3 0.08 12.19);
  --pink-200: oklch(0.4 0.1 13.07);
  --pink-300: oklch(0.5 0.12 13.88);
  --pink-400: oklch(0.6 0.14 14.37);
  --pink-500: oklch(0.7 0.13 3.57);
  --pink-600: oklch(0.78 0.09 358.19);
  --pink-700: oklch(0.84 0.06 356.17);
  --pink-800: oklch(0.89 0.04 356.36);
  --pink-900: oklch(0.93 0.025 352.87);

  /* Text colors optimized for dark backgrounds with higher contrast */
  --text-50: oklch(0.18 0.015 252.71);
  --text-100: oklch(0.25 0.018 252.59);
  --text-200: oklch(0.35 0.02 251.43);
  --text-300: oklch(0.45 0.022 251.42);
  --text-400: oklch(0.58 0.02 251.56);
  --text-500: oklch(0.7 0.015 252.95);
  --text-600: oklch(0.8 0.01 252.82);
  --text-700: oklch(0.87 0.008 255.48);
  --text-800: oklch(0.92 0.005 228.78);
  --text-900: oklch(0.96 0.003 247.84);

  /* Success colors with improved readability */
  --success-50: oklch(0.35 0.08 146.89);
  --success-100: oklch(0.42 0.1 146.43);
  --success-200: oklch(0.5 0.12 146.24);
  --success-300: oklch(0.58 0.13 146.44);
  --success-400: oklch(0.68 0.14 146.61);
  --success-500: oklch(0.75 0.14 147.32);
  --success-600: oklch(0.82 0.12 147.8);
  --success-700: oklch(0.87 0.095 148.43);
  --success-800: oklch(0.91 0.07 148.83);
  --success-900: oklch(0.95 0.04 151.19);

  /* Warning colors enhanced for dark mode */
  --warning-50: oklch(0.45 0.1 54.96);
  --warning-100: oklch(0.52 0.11 62.62);
  --warning-200: oklch(0.6 0.12 71.02);
  --warning-300: oklch(0.68 0.125 78.12);
  --warning-400: oklch(0.75 0.13 87.12);
  --warning-500: oklch(0.82 0.125 92.22);
  --warning-600: oklch(0.87 0.11 95.11);
  --warning-700: oklch(0.9 0.085 96.83);
  --warning-800: oklch(0.93 0.06 96.59);
  --warning-900: oklch(0.96 0.035 97.76);

  /* Danger colors with better visibility and more vibrant in dark mode */
  --danger-50: oklch(0.25 0.1 26.61);
  --danger-100: oklch(0.32 0.12 26.53);
  --danger-200: oklch(0.4 0.13 25.84);
  --danger-300: oklch(0.48 0.12 24.52);
  --danger-400: oklch(0.58 0.115 22.84);
  --danger-500: oklch(0.68 0.11 21.1);
  --danger-600: oklch(0.76 0.09 19.5306);
  --danger-700: oklch(0.82 0.07 18.39);
  --danger-800: oklch(0.87 0.05 17.7147);
  --danger-900: oklch(0.92 0.03 17.34);

  --background: oklch(0.12 0.008 152.01);
  --foreground: var(--text-800);
}


@theme {
  /* Primary colors with full scale */
  --color-primary-50: var(--primary-50);
  --color-primary-100: var(--primary-100);
  --color-primary-200: var(--primary-200);
  --color-primary-300: var(--primary-300);
  --color-primary-400: var(--primary-400);
  --color-primary-500: var(--primary-500);
  --color-primary-600: var(--primary-600);
  --color-primary-700: var(--primary-700);
  --color-primary-800: var(--primary-800);
  --color-primary-900: var(--primary-900);

  --color-primary: var(--primary-500);
  --color-primary-hover: var(--primary-600);
  --color-primary-active: var(--primary-700);
  --color-primary-focus: var(--primary-800);

  /* Gray scale with full range */
  --color-gray-50: var(--gray-50);
  --color-gray-100: var(--gray-100);
  --color-gray-200: var(--gray-200);
  --color-gray-300: var(--gray-300);
  --color-gray-400: var(--gray-400);
  --color-gray-500: var(--gray-500);
  --color-gray-600: var(--gray-600);
  --color-gray-700: var(--gray-700);
  --color-gray-800: var(--gray-800);
  --color-gray-900: var(--gray-900);

  --color-disabled: var(--gray-200);

  /* Secondary colors - lighter for light mode */
  --color-secondary: var(--gray-50);
  --color-secondary-hover: var(--gray-100);
  --color-secondary-active: var(--gray-200);
  --color-secondary-focus: var(--gray-300);

  /* Status colors with full scales */
  --color-success-50: var(--success-50);
  --color-success-100: var(--success-100);
  --color-success-200: var(--success-200);
  --color-success-300: var(--success-300);
  --color-success-400: var(--success-400);
  --color-success-500: var(--success-500);
  --color-success-600: var(--success-600);
  --color-success-700: var(--success-700);
  --color-success-800: var(--success-800);
  --color-success-900: var(--success-900);

  --color-success: var(--success-500);
  --color-success-hover: var(--success-600);
  --color-success-active: var(--success-700);
  --color-success-focus: var(--success-800);

  --color-warning-50: var(--warning-50);
  --color-warning-100: var(--warning-100);
  --color-warning-200: var(--warning-200);
  --color-warning-300: var(--warning-300);
  --color-warning-400: var(--warning-400);
  --color-warning-500: var(--warning-500);
  --color-warning-600: var(--warning-600);
  --color-warning-700: var(--warning-700);
  --color-warning-800: var(--warning-800);
  --color-warning-900: var(--warning-900);

  --color-warning: var(--warning-500);
  --color-warning-hover: var(--warning-600);
  --color-warning-active: var(--warning-700);
  --color-warning-focus: var(--warning-800);

  --color-danger-50: var(--danger-50);
  --color-danger-100: var(--danger-100);
  --color-danger-200: var(--danger-200);
  --color-danger-300: var(--danger-300);
  --color-danger-400: var(--danger-400);
  --color-danger-500: var(--danger-500);
  --color-danger-600: var(--danger-600);
  --color-danger-700: var(--danger-700);
  --color-danger-800: var(--danger-800);
  --color-danger-900: var(--danger-900);

  --color-danger: var(--danger-600);
  --color-danger-hover: var(--danger-700);
  --color-danger-active: var(--danger-800);
  --color-danger-focus: var(--danger-700);

  --color-info-50: var(--blue-50);
  --color-info-100: var(--blue-100);
  --color-info-200: var(--blue-200);
  --color-info-300: var(--blue-300);
  --color-info-400: var(--blue-400);
  --color-info-500: var(--blue-500);
  --color-info-600: var(--blue-600);
  --color-info-700: var(--blue-700);
  --color-info-800: var(--blue-800);
  --color-info-900: var(--blue-900);

  --color-info: var(--blue-500);
  --color-info-hover: var(--blue-600);
  --color-info-active: var(--blue-700);
  --color-info-focus: var(--blue-800);

  /* Surface colors */
  --color-surface: var(--gray-50);
  --color-base: var(--background);

  /* Text colors with full scale */
  --color-text-50: var(--text-50);
  --color-text-100: var(--text-100);
  --color-text-200: var(--text-200);
  --color-text-300: var(--text-300);
  --color-text-400: var(--text-400);
  --color-text-500: var(--text-500);
  --color-text-600: var(--text-600);
  --color-text-700: var(--text-700);
  --color-text-800: var(--text-800);
  --color-text-900: var(--text-900);

  --color-foreground: var(--foreground);
  --color-muted: var(--text-600);
  --color-muted-foreground: var(--text-500);

  /* Border colors with better contrast but lighter in light mode */
  --color-border: var(--gray-300);
  --color-border-secondary: var(--gray-200);
  --color-border-muted: var(--gray-100);

  /* Ring colors for focus states */
  --color-ring: var(--primary-500);
  --color-ring-offset: var(--background);

  /* Surface colors with better light mode appearance */
  --color-surface: var(--gray-50);
  --color-base: var(--background);

}

/* Dark mode specific overrides within theme */
.dark {
  --color-surface: var(--gray-100);
  --color-secondary: var(--gray-200);
  --color-secondary-hover: var(--gray-300);
  --color-secondary-active: var(--gray-400);
  --color-border: var(--gray-400);
  --color-border-secondary: var(--gray-300);
  --color-border-muted: var(--gray-200);

  /* Darker danger colors for dark mode buttons */
  --color-danger: var(--danger-400);
  --color-danger-hover: var(--danger-500);
  --color-danger-active: var(--danger-600);
  --color-danger-focus: var(--danger-500);
}

/* Base styling for the document */
body {
  background-color: var(--color-base);
  color: var(--color-foreground);
  font-family: "Lato", "Geist", ui-sans-serif, system-ui, sans-serif;
  line-height: 1.6;
}

/* Utility classes for responsive design */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Mobile-first responsive utilities */
@media (max-width: 640px) {
  .mobile-stack>*+* {
    margin-top: 0.75rem;
  }

  .mobile-full-width {
    width: 100%;
  }
}